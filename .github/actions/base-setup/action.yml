name: "Base Setup"
description: "Set up the base environment"
inputs:
  node-version:
    description: "Node.js version"
    required: true
  python-version:
    description: "Python version"
    required: true

runs:
  using: "composite"
  steps:
    - name: Cache Dependencies
      uses: useblacksmith/cache@v5
      with:
        path: ./dependencies
        key: ${{ runner.os }}-dependencies-${{ hashFiles('./soldeer.lock') }}

    - uses: pnpm/action-setup@v4
      with:
        version: 9

    - name: Install Node.js
      uses: useblacksmith/setup-node@v5
      with:
        node-version: ${{ inputs.node-version }}
        cache: pnpm

    - name: Install Python
      uses: useblacksmith/setup-python@v6
      if: ${{ inputs.python-version != '' }}
      with:
        python-version: ${{ inputs.python-version }}
        cache: pip

    - name: Install Python dependencies
      if: ${{ inputs.python-version != '' }}
      run: pip install -r requirements.txt
      shell: bash

    - name: Install Foundry
      uses: penandlim/foundry-toolchain@master
      with:
        version: stable

    - name: Install Soldeer dependencies
      run: forge soldeer install
      shell: bash

    - name: Install dependencies
      run: pnpm install
      shell: bash
