name: CI

on:
  workflow_dispatch:
  push:
    branches: [master, staging]
  pull_request:
    branches: ["**"]

env:
  FOUNDRY_PROFILE: ci
  NODE_VERSION: 20.14.0
  PYTHON_VERSION: 3.9.17

concurrency:
  group: ${{ github.event_name == 'pull_request' && 'PR' || 'push' }}-${{ github.head_ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

jobs:
  lint:
    name: Lint
    runs-on: blacksmith-2vcpu-ubuntu-2404
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Base setup
        uses: ./.github/actions/base-setup
        with:
          node-version: ${{ env.NODE_VERSION }}
      - name: Lint all source files
        run: pnpm run lint

  slither:
    name: Slither
    runs-on: blacksmith-2vcpu-ubuntu-2404
    permissions:
      contents: read
      packages: read
      pull-requests: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Base setup
        uses: ./.github/actions/base-setup
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Generate build info for slither
        run: pnpm run build:slither

      - name: Run Slither
        uses: crytic/slither-action@v0.4.1
        id: slither
        with:
          # TODO: re-enable this before audits
          fail-on: none
          ignore-compile: true
          node-version: ${{ env.NODE_VERSION }}
          slither-args:
            --checklist --markdown-root ${{ github.server_url }}/${{ github.repository }}/blob/${{ github.sha }}/
          slither-config: slither.config.json

      - name: Create/update checklist as PR comment
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        env:
          REPORT: ${{ steps.slither.outputs.stdout }}
        with:
          script: |
            const script = require('.github/scripts/comment')
            const header = '# Slither report'
            const body = process.env.REPORT
            await script({ github, context, header, body })

            // Check if the report contains findings
            if (body.includes('results)')) {
              core.setFailed('Slither found issues. Please review the report.');
            }

  semgrep:
    name: Semgrep
    runs-on: blacksmith-2vcpu-ubuntu-2404
    permissions: write-all
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Base setup
        uses: ./.github/actions/base-setup
        with:
          node-version: ${{ env.NODE_VERSION }}
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Run Semgrep
        id: semgrep
        run: |
          semgrep --config p/smart-contracts --exclude deps/ src/ -o semgrep.out --text
          RAW_REPORT=$(cat semgrep.out)

          if [ "$RAW_REPORT" != "" ]; then
            # Find the line with "Code Findings" and insert markdown
            LINE_NUMBER=$(echo "$RAW_REPORT" | grep -n "Code Findings" | cut -d: -f1)
            if [ -n "$LINE_NUMBER" ]; then
              LINE_NUMBER=$((LINE_NUMBER + 2))
              RAW_REPORT=$(echo "$RAW_REPORT" | sed "${LINE_NUMBER}i<details>\n<summary><strong>Full semgrep report</strong> 👇</summary>\n<br />")
              RAW_REPORT="${RAW_REPORT}\n</details>"
            fi

            echo "$RAW_REPORT"
            EOF=$(dd if=/dev/urandom bs=15 count=1 status=none | base64)
            echo "report<<$EOF" >> $GITHUB_OUTPUT
            echo "# Semgrep report" >> $GITHUB_OUTPUT
            echo "$RAW_REPORT" >> $GITHUB_OUTPUT
            echo "$EOF" >> $GITHUB_OUTPUT
          fi
        env:
          SEMGREP_SEND_METRICS: off
      - name: Add semgrep to sticky comment
        if: github.event_name == 'pull_request' || github.event_name == 'pull_request_target'
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: semgrep
          delete: ${{ !steps.semgrep.outputs.report }}
          message: ${{ steps.semgrep.outputs.report }}
      - name: Fail if Semgrep report is not empty
        if: ${{ steps.semgrep.outputs.report }}
        run: |
          echo "Semgrep found issues. Failing the action."
          exit 1

  test:
    name: Test
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Base setup
        uses: ./.github/actions/base-setup
        with:
          node-version: ${{ env.NODE_VERSION }}
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Run Forge build and tests
        run: |
          forge --version
          pnpm build
          forge test -vvv
        env:
          # make fuzzing semi-deterministic to avoid noisy gas cost estimation
          # due to non-deterministic fuzzing (but still use pseudo-random fuzzing seeds)
          FOUNDRY_FUZZ_SEED: 0x${{ github.event.pull_request.base.sha || github.sha }}
          MAINNET_RPC_URL: ${{ secrets.MAINNET_RPC_URL }}

  invariant:
    name: Invariant
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Base setup
        uses: ./.github/actions/base-setup
        with:
          node-version: ${{ env.NODE_VERSION }}
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Run Forge build and tests
        run: |
          forge --version
          pnpm build
          forge test -vvv
        env:
          # make fuzzing semi-deterministic to avoid noisy gas cost estimation
          # due to non-deterministic fuzzing (but still use pseudo-random fuzzing seeds)
          FOUNDRY_FUZZ_SEED: 0x${{ github.event.pull_request.base.sha || github.sha }}
          MAINNET_RPC_URL: ${{ secrets.MAINNET_RPC_URL }}
          FOUNDRY_PROFILE: invariant

  compare-gas-reports:
    name: Compare gas reports
    runs-on: blacksmith-16vcpu-ubuntu-2404
    permissions: write-all
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Base setup
        uses: ./.github/actions/base-setup
        with:
          node-version: ${{ env.NODE_VERSION }}
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Run Forge build and tests
        run: |
          forge --version
          pnpm build
          forge test --gas-report --fuzz-seed 0 | tee gasreport.ansi
        env:
          MAINNET_RPC_URL: ${{ secrets.MAINNET_RPC_URL }}

      - name: Compare gas reports
        if: success() || failure()
        uses: Rubilmax/foundry-gas-diff@v3.21
        with:
          summaryQuantile: 0.8 # only display the 20% most significant gas diffs in the summary (defaults to 20%)
          sortCriteria: avg,max # sort diff rows by criteria
          sortOrders: desc,asc # and directions
          match: src/**/* # match gas reports from specific paths
        id: gas_diff

      - name: Add gas diff to sticky comment
        if:
          (success() || failure()) && (github.event_name == 'pull_request' || github.event_name ==
          'pull_request_target')
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          # delete the comment in case changes no longer impact gas costs
          delete: ${{ !steps.gas_diff.outputs.markdown }}
          message: ${{ steps.gas_diff.outputs.markdown }}

  coverage:
    name: Coverage
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Base setup
        uses: ./.github/actions/base-setup
        with:
          node-version: ${{ env.NODE_VERSION }}
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Run Forge Coverage
        run: |
          pnpm build
          pnpm run coverage
        env:
          # make fuzzing semi-deterministic to avoid noisy gas cost estimation
          # due to non-deterministic fuzzing (but still use pseudo-random fuzzing seeds)
          FOUNDRY_FUZZ_SEED: 0x${{ github.event.pull_request.base.sha || github.sha }}
          MAINNET_RPC_URL: ${{ secrets.MAINNET_RPC_URL }}

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

  spelling:
    name: Spelling
    runs-on: blacksmith-2vcpu-ubuntu-2404
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Run codespell
        uses: codespell-project/actions-codespell@v2
