name: Docs

on:
  push:
    branches: [master]

env:
  FOUNDRY_PROFILE: ci
  NODE_VERSION: 20.14.0
  PYTHON_VERSION: 3.9.17

jobs:
  deploy:
    name: Deploy forge doc to GitHub Pages
    runs-on: blacksmith-2vcpu-ubuntu-2404
    permissions:
      contents: write # To push a branch
      pages: write # To push to a GitHub Pages site
      id-token: write # To update the deployment status
    steps:
      - name: Checkout repository without submodules
        uses: actions/checkout@v4
        with:
          submodules: false
      - name: Base setup
        uses: ./.github/actions/base-setup
        with:
          node-version: ${{ env.NODE_VERSION }}
          python-version: ${{ env.PYTHON_VERSION }}
      - name: Build forge docs
        run: |
          pnpm build
          forge doc --build
      - name: Setup Pages
        uses: actions/configure-pages@v5
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          # Upload entire repository
          path: "docs/book"
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
