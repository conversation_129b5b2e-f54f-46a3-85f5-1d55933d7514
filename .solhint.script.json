{"extends": "solhint:recommended", "rules": {"gas-custom-errors": "off", "reason-string": "off", "code-complexity": ["warn", 12], "compiler-version": ["error", ">=0.8.18"], "func-name-mixedcase": "off", "func-visibility": ["error", {"ignoreConstructors": true}], "max-line-length": ["warn", 121], "named-parameters-mapping": "off", "no-console": "off", "not-rely-on-time": "off", "func-param-name-mixedcase": "error", "modifier-name-mixedcase": "error", "private-vars-leading-underscore": "warn", "var-name-mixedcase": "warn", "imports-on-top": "error", "const-name-snakecase": "warn", "no-unused-vars": "error", "no-unused-import": "error", "immutable-vars-naming": ["error", {"immutablesAsConstants": true}]}}