{"extends": "solhint:recommended", "rules": {"no-complex-fallback": "off", "gas-custom-errors": "off", "reason-string": "off", "no-empty-blocks": "off", "code-complexity": ["warn", 8], "compiler-version": ["error", ">=0.8.18"], "func-name-mixedcase": "off", "func-visibility": ["error", {"ignoreConstructors": true}], "max-line-length": ["warn", 121], "named-parameters-mapping": "off", "no-console": "off", "not-rely-on-time": "off", "func-param-name-mixedcase": "error", "modifier-name-mixedcase": "error", "private-vars-leading-underscore": "off", "var-name-mixedcase": "off", "imports-on-top": "error", "const-name-snakecase": "error", "no-unused-vars": "error", "no-unused-import": "warn", "immutable-vars-naming": ["warn", {"immutablesAsConstants": true}], "max-states-count": "off"}}