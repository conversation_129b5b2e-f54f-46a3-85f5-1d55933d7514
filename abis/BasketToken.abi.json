[{"type": "constructor", "inputs": [], "stateMutability": "payable"}, {"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_PLUGINS_PER_ACCOUNT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "PLUGIN_CALL_GAS_LIMIT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "addPlugin", "inputs": [{"name": "plugin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "assetRegistry", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "basketManager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "bitFlag", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "cancelDepositRequest", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "cancelRedeemRequest", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimFallbackAssets", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimFallbackShares", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimableDepositRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimableFallbackAssets", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimableFallbackShares", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimableRedeemRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToAssets", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToShares", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "pure"}, {"type": "function", "name": "deposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "eip712Domain", "inputs": [], "outputs": [{"name": "fields", "type": "bytes1", "internalType": "bytes1"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "verifyingContract", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "extensions", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "fallbackDepositTriggered", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "fallback<PERSON><PERSON><PERSON>Triggered", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "fulfillDeposit", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "fulfillRedeem", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAssets", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getDepositRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct BasketToken.DepositRequestView", "components": [{"name": "totalDepositAssets", "type": "uint256", "internalType": "uint256"}, {"name": "fulfilledShares", "type": "uint256", "internalType": "uint256"}, {"name": "fallbackTriggered", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getRedeemRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct BasketToken.RedeemRequestView", "components": [{"name": "totalRedeemShares", "type": "uint256", "internalType": "uint256"}, {"name": "fulfilledAssets", "type": "uint256", "internalType": "uint256"}, {"name": "fallbackTriggered", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getTargetWeights", "inputs": [], "outputs": [{"name": "", "type": "uint64[]", "internalType": "uint64[]"}], "stateMutability": "view"}, {"type": "function", "name": "harvestManagementFee", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasPlugin", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "plugin", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "asset_", "type": "address", "internalType": "contract IERC20"}, {"name": "name_", "type": "string", "internalType": "string"}, {"name": "symbol_", "type": "string", "internalType": "string"}, {"name": "bitFlag_", "type": "uint256", "internalType": "uint256"}, {"name": "strategy_", "type": "address", "internalType": "address"}, {"name": "assetRegistry_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isOperator", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lastDepositRequestId", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "lastManagementFeeHarvestTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint40", "internalType": "uint40"}], "stateMutability": "view"}, {"type": "function", "name": "lastRedeemRequestId", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxDeposit", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxMint", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON>", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "multicall", "inputs": [{"name": "data", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [{"name": "results", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nextDepositRequestId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "nextRedeemRequestId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "pendingDepositRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "pendingRedeemRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "permit2", "inputs": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "pluginAt", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pluginBalanceOf", "inputs": [{"name": "plugin", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "plugins", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "pluginsCount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "prepareForRebalance", "inputs": [{"name": "feeBps", "type": "uint16", "internalType": "uint16"}, {"name": "feeCollector", "type": "address", "internalType": "address"}], "outputs": [{"name": "pendingDeposits", "type": "uint256", "internalType": "uint256"}, {"name": "pendingShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "previewDeposit", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "previewMint", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "previewRedeem", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "previewWithdraw", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "proRataRedeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "from", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeAllPlugins", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removePlugin", "inputs": [{"name": "plugin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "requestDeposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "requestRedeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "setBitFlag", "inputs": [{"name": "bitFlag_", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "share", "inputs": [], "outputs": [{"name": "shareTokenAddress", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "strategy", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceID", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalPendingDeposits", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalPendingRedemptions", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BitFlagUpdated", "inputs": [{"name": "oldBitFlag", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "newBitFlag", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DepositFallbackTriggered", "inputs": [{"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DepositFulfilled", "inputs": [{"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DepositRequest", "inputs": [{"name": "controller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "sender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DepositRequestQueued", "inputs": [{"name": "depositRequestId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "pendingDeposits", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "EIP712DomainChanged", "inputs": [], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "ManagementFeeHarvested", "inputs": [{"name": "fee", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OperatorSet", "inputs": [{"name": "controller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "PluginAdded", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "plugin", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PluginRemoved", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "plugin", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RedeemFallbackTriggered", "inputs": [{"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RedeemFulfilled", "inputs": [{"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RedeemRequest", "inputs": [{"name": "controller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "sender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RedeemRequestQueued", "inputs": [{"name": "redeemRequestId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "pendingShares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Withdraw", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AssetPaused", "inputs": []}, {"type": "error", "name": "CannotFulfillWithZeroAssets", "inputs": []}, {"type": "error", "name": "CannotFulfillWithZeroShares", "inputs": []}, {"type": "error", "name": "DepositRequestAlreadyFulfilled", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC2612ExpiredSignature", "inputs": [{"name": "deadline", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC2612InvalidSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC4626ExceededMaxDeposit", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxMint", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxRedeem", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC4626ExceededMaxWithdraw", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "FailedCall", "inputs": []}, {"type": "error", "name": "IndexOutOfBounds", "inputs": []}, {"type": "error", "name": "InvalidAccountNonce", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "currentNonce", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidManagementFee", "inputs": []}, {"type": "error", "name": "InvalidPluginAddress", "inputs": []}, {"type": "error", "name": "InvalidTokenInPlugin", "inputs": []}, {"type": "error", "name": "MustClaimFullAmount", "inputs": []}, {"type": "error", "name": "MustClaimOutstandingDeposit", "inputs": []}, {"type": "error", "name": "MustClaimOutstandingRedeem", "inputs": []}, {"type": "error", "name": "NotAuthorizedOperator", "inputs": []}, {"type": "error", "name": "NotBasketManager", "inputs": []}, {"type": "error", "name": "NotFeeCollector", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "PluginAlreadyAdded", "inputs": []}, {"type": "error", "name": "PluginNotFound", "inputs": []}, {"type": "error", "name": "PluginsLimitReachedForAccount", "inputs": []}, {"type": "error", "name": "PreviousDepositRequestNotFulfilled", "inputs": []}, {"type": "error", "name": "PreviousRedeemRequestNotFulfilled", "inputs": []}, {"type": "error", "name": "RedeemRequestAlreadyFulfilled", "inputs": []}, {"type": "error", "name": "ReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UnsafeCast", "inputs": []}, {"type": "error", "name": "ZeroAddress", "inputs": []}, {"type": "error", "name": "ZeroAmount", "inputs": []}, {"type": "error", "name": "ZeroClaimableFallbackAssets", "inputs": []}, {"type": "error", "name": "ZeroClaimableFallbackShares", "inputs": []}, {"type": "error", "name": "ZeroPendingDeposits", "inputs": []}, {"type": "error", "name": "ZeroPendingRedeems", "inputs": []}, {"type": "error", "name": "ZeroPluginsLimit", "inputs": []}]