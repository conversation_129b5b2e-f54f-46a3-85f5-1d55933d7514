[{"type": "function", "name": "getTargetWeights", "inputs": [{"name": "bitFlag", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "targetWeights", "type": "uint64[]", "internalType": "uint64[]"}], "stateMutability": "view"}, {"type": "function", "name": "supportsBitFlag", "inputs": [{"name": "bitFlag", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "supported", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}]