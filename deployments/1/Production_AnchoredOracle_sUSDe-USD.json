{"address": "0x37BD278CE3F0025fC18086dA11e4C5f8f8B2b885", "abi": [{"inputs": [{"internalType": "address", "name": "_primary<PERSON><PERSON>le", "type": "address"}, {"internalType": "address", "name": "_anchor<PERSON><PERSON>le", "type": "address"}, {"internalType": "uint256", "name": "_maxDivergence", "type": "uint256"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "anchorOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxDivergence", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "primaryO<PERSON>le", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "AnchoredOracle_ScalingOverflow", "type": "error"}, {"inputs": [], "name": "PriceOracle_InvalidAnswer", "type": "error"}, {"inputs": [], "name": "PriceOracle_InvalidConfiguration", "type": "error"}], "bytecode": "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", "args_data": "0x0000000000000000000000000ef805362304d1a1ef1444f5b411fe96dec663eb000000000000000000000000e2c3844529c4f5b77fbc692fe1456136afd6afb00000000000000000000000000000000000000000000000000011c37937e08000", "tx_hash": "", "args": [], "data": "", "artifact_path": "AnchoredOracle.sol", "artifact_full_path": "AnchoredOracle.sol:AnchoredOracle"}