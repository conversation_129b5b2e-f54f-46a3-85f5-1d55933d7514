{"address": "0xD57950e8E9111cA49B1a041eD6c53a1355e317C7", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "addAsset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "enabledAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllAssets", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "getAssetStatus", "outputs": [{"internalType": "enum AssetRegistry.AssetStatus", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "bitFlag", "type": "uint256"}], "name": "getAssets", "outputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}], "name": "getAssetsBitFlag", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "bitFlag", "type": "uint256"}], "name": "hasPausedAssets", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "enum AssetRegistry.AssetStatus", "name": "newStatus", "type": "uint8"}], "name": "setAssetStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "asset", "type": "address"}], "name": "AddAsset", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "asset", "type": "address"}, {"indexed": false, "internalType": "enum AssetRegistry.AssetStatus", "name": "status", "type": "uint8"}], "name": "SetAssetStatus", "type": "event"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "AssetAlreadyEnabled", "type": "error"}, {"inputs": [], "name": "AssetExceedsMaximum", "type": "error"}, {"inputs": [], "name": "AssetInvalidStatusUpdate", "type": "error"}, {"inputs": [], "name": "AssetNotEnabled", "type": "error"}, {"inputs": [], "name": "<PERSON><PERSON>setsReached", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}], "bytecode": "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", "args_data": "0x0000000000000000000000008842fe65a7db9bb5de6d50e49af19496da09f9b5", "tx_hash": "", "args": [], "data": "", "artifact_path": "AssetRegistry.sol", "artifact_full_path": "AssetRegistry.sol:AssetRegistry"}