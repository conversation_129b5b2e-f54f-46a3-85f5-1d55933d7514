{"address": "0x0fFCBFe3034E9f7139c367d52f2668b6B878A82F", "abi": [{"inputs": [{"internalType": "address", "name": "_base", "type": "address"}, {"internalType": "address", "name": "_quote", "type": "address"}, {"internalType": "address", "name": "_feed", "type": "address"}, {"internalType": "uint256", "name": "_maxStaleness", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "base", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feed", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxStaleness", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quote", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PriceOracle_InvalidAnswer", "type": "error"}, {"inputs": [], "name": "PriceOracle_InvalidConfiguration", "type": "error"}, {"inputs": [{"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "PriceOracle_NotSupported", "type": "error"}, {"inputs": [], "name": "PriceOracle_Overflow", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "staleness", "type": "uint256"}, {"internalType": "uint256", "name": "maxStaleness", "type": "uint256"}], "name": "PriceOracle_TooStale", "type": "error"}], "bytecode": "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", "args_data": "0x000000000000000000000000dc035d45d973e3ec169d2276ddab16f1e407384f0000000000000000000000000000000000000000000000000000000000000348000000000000000000000000ff30586cd0f29ed462364c7e81375fc0c71219b10000000000000000000000000000000000000000000000000000000000015180", "tx_hash": "", "args": [], "data": "", "artifact_path": "ChainlinkOracle.sol", "artifact_full_path": "ChainlinkOracle.sol:ChainlinkOracle"}