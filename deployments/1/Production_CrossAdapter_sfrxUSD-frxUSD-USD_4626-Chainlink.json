{"address": "0x1A52fa88244E861914883C21577315E652F09C66", "abi": [{"inputs": [{"internalType": "address", "name": "_base", "type": "address"}, {"internalType": "address", "name": "_cross", "type": "address"}, {"internalType": "address", "name": "_quote", "type": "address"}, {"internalType": "address", "name": "_oracleBaseCross", "type": "address"}, {"internalType": "address", "name": "_oracleCrossQuote", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "base", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "cross", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "oracleBaseCross", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "oracleCrossQuote", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quote", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "PriceOracle_NotSupported", "type": "error"}], "bytecode": "0x610120346100fe57601f6107e138819003918201601f19168301916001600160401b038311848410176101025780849260a0946040528339810103126100fe5761004881610116565b9061005560208201610116565b61006160408301610116565b9061007a608061007360608601610116565b9401610116565b9360805260a05260c05260e052610100526040516106b6908161012b82396080518181816101c101526102ec015260a05181818160d70152818161036701526104da015260c05181818161013b015261030e015260e051818181609001528181610413015261051701526101005181818161017e015281816103a301526105870152f35b5f80fd5b634e487b7160e01b5f52604160045260245ffd5b51906001600160a01b03821682036100fe5756fe60806040526004361015610011575f80fd5b5f3560e01c80630579e61f1461026e57806306fdde03146101e55780635001f3b5146101a25780636f612f9a1461015f578063999b93af1461011c578063ae68676c146100fb578063fa074d03146100b85763fd88670014610071575f80fd5b346100b4575f3660031901126100b45760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b5f80fd5b346100b4575f3660031901126100b45760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b346100b457602061011461010e3661028e565b916102ea565b604051908152f35b346100b4575f3660031901126100b45760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b346100b4575f3660031901126100b45760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b346100b4575f3660031901126100b45760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b346100b4575f3660031901126100b4576040516040810181811067ffffffffffffffff82111761025a57604052600c8152604060208201916b21b937b9b9a0b230b83a32b960a11b83528151928391602083525180918160208501528484015e5f828201840152601f01601f19168101030190f35b634e487b7160e01b5f52604160045260245ffd5b346100b457604061028161010e3661028e565b8151908082526020820152f35b60609060031901126100b457600435906024356001600160a01b03811681036100b457906044356001600160a01b03811681036100b45790565b90601f8019910116810190811067ffffffffffffffff82111761025a57604052565b7f0000000000000000000000000000000000000000000000000000000000000000927f00000000000000000000000000000000000000000000000000000000000000009261033b91849186906105ed565b156104b357604051632b9a19db60e21b815260048101919091526001600160a01b0391821660248201527f0000000000000000000000000000000000000000000000000000000000000000918216604482015290602082806064810103816001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000165afa8015610475575f90610480575b604051632b9a19db60e21b815260048101919091526001600160a01b0391821660248201529216604483015250602081806064810103816001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000165afa908115610475575f91610446575090565b90506020813d60201161046d575b81610461602093836102c8565b810103126100b4575190565b3d9150610454565b6040513d5f823e3d90fd5b506020823d6020116104ab575b8161049a602093836102c8565b810103126100b457602091516103d2565b3d915061048d565b604051632b9a19db60e21b815260048101919091526001600160a01b0392831660248201527f000000000000000000000000000000000000000000000000000000000000000092831660448201529091602082806064810103816001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000165afa8015610475575f906105ba575b604051632b9a19db60e21b815260048101919091526001600160a01b0391821660248201529216604483015250602081806064810103816001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000165afa908115610475575f91610446575090565b506020823d6020116105e5575b816105d4602093836102c8565b810103126100b45760209151610546565b3d91506105c7565b906001600160a01b0380919493941691169181831480610665575b61065d576001600160a01b03168214908161064a575b50610643576001600160a01b0392506304ca22af60e41b5f526004521660245260445ffd5b5050600190565b90506001600160a01b038316145f61061e565b505050505f90565b506001600160a01b0381166001600160a01b0385161461060856fea2646970667358221220e7553994a52afbc5cb705800cd09b5820cd940518f11a4b07f2aaa4e956bd25864736f6c634300081c0033", "args_data": "0x000000000000000000000000cf62f905562626cfcdd2261162a51fd02fc9c5b6000000000000000000000000cacd6fd266af91b8aed52accc382b4e165586e2900000000000000000000000000000000000000000000000000000000000003480000000000000000000000004e1ac601becc4f55d676357fedb759ec240a2c57000000000000000000000000b88ecec6c6d9b217a657f3eb82e1c79ec1785f84", "tx_hash": "", "args": [], "data": "", "artifact_path": "CrossAdapter.sol", "artifact_full_path": "CrossAdapter.sol:CrossAdapter"}