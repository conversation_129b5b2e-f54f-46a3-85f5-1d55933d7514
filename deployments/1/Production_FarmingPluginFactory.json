{"address": "0xD46D6161bBB2103Da01987430de8B9C4756DF8f6", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "manager", "type": "address"}, {"internalType": "address", "name": "_defaultPluginOwner", "type": "address"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "allPlugins", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IERC20Plugins", "name": "stakingToken", "type": "address"}, {"internalType": "contract IERC20", "name": "rewardsToken", "type": "address"}], "name": "computePluginAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "defaultPluginOwner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IERC20Plugins", "name": "stakingToken", "type": "address"}, {"internalType": "contract IERC20", "name": "rewardsToken", "type": "address"}, {"internalType": "address", "name": "pluginOwner", "type": "address"}], "name": "deployFarmingPlugin", "outputs": [{"internalType": "address", "name": "plugin", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IERC20Plugins", "name": "stakingToken", "type": "address"}, {"internalType": "contract IERC20", "name": "rewardsToken", "type": "address"}], "name": "deployFarmingPluginWithDefaultOwner", "outputs": [{"internalType": "address", "name": "plugin", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "plugins", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pluginOwner", "type": "address"}], "name": "setDefaultPluginOwner", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "DefaultPluginOwnerSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "stakingToken", "type": "address"}, {"indexed": true, "internalType": "address", "name": "rewardsToken", "type": "address"}, {"indexed": true, "internalType": "address", "name": "plugin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "pluginOwner", "type": "address"}], "name": "FarmingPluginCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}], "bytecode": "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", "args_data": "0x0000000000000000000000008842fe65a7db9bb5de6d50e49af19496da09f9b50000000000000000000000008842fe65a7db9bb5de6d50e49af19496da09f9b50000000000000000000000007bd578354b0b2f02e656f1bdc0e41a80f860534b", "tx_hash": "", "args": [], "data": "", "artifact_path": "FarmingPluginFactory.sol", "artifact_full_path": "FarmingPluginFactory.sol:FarmingPluginFactory"}