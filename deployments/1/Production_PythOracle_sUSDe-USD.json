{"address": "0x0eF805362304d1a1EF1444f5b411FE96Dec663eB", "abi": [{"inputs": [{"internalType": "address", "name": "_pyth", "type": "address"}, {"internalType": "address", "name": "_base", "type": "address"}, {"internalType": "address", "name": "_quote", "type": "address"}, {"internalType": "bytes32", "name": "_feedId", "type": "bytes32"}, {"internalType": "uint256", "name": "_maxStaleness", "type": "uint256"}, {"internalType": "uint256", "name": "_maxConfWidth", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "base", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feedId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxStaleness", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pyth", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quote", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PriceOracle_InvalidAnswer", "type": "error"}, {"inputs": [], "name": "PriceOracle_InvalidConfiguration", "type": "error"}, {"inputs": [{"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "PriceOracle_NotSupported", "type": "error"}, {"inputs": [], "name": "PriceOracle_Overflow", "type": "error"}], "bytecode": "0x610180806040523461015e5760c081610b2880380380916100208285610162565b83398101031261015e5761003381610199565b9061004060208201610199565b9061004d60408201610199565b90606081015160a0608083015192015192610384831161014457600a84108015610153575b6101445761009c956080528460a05260c05260e052610100526101205260018060a01b03166101ad565b6101405260c0516100b5906001600160a01b03166101ad565b610160526040516108c390816102658239608051818181609b01526103c8015260a0518181816101ba015261033f015260c051818181610103015261031d015260e0518181816101f50152610394015261010051818181610178015261041601526101205181818161013e01526105f8015261014051816104810152610160518181816104c201526105490152f35b6301a4c16560e21b5f5260045ffd5b506101f48411610072565b5f80fd5b601f909101601f19168101906001600160401b0382119082101761018557604052565b634e487b7160e01b5f52604160045260245ffd5b51906001600160a01b038216820361015e57565b63ffffffff6001600160a01b038216111561024b575f8091604051602081019063313ce56760e01b8252600481526101e6602482610162565b51915afa3d1561025c573d906001600160401b0382116101855760405191610218601f8201601f191660200184610162565b82523d5f602084013e5b80610251575b1561024b5760208180518101031261015e576020015160ff8116810361015e5790565b50601290565b506020815114610228565b60609061022256fe60806040526004361015610011575f80fd5b5f3560e01c80630579e61f1461029f57806306fdde03146102185780634a643499146101de5780635001f3b51461019b57806387cf46961461016157806388df1eff14610127578063999b93af146100e4578063ae68676c146100c35763f98d06f01461007c575f80fd5b346100bf575f3660031901126100bf5760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b5f80fd5b346100bf5760206100dc6100d6366102bf565b91610315565b604051908152f35b346100bf575f3660031901126100bf5760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b346100bf575f3660031901126100bf5760206040517f00000000000000000000000000000000000000000000000000000000000000008152f35b346100bf575f3660031901126100bf5760206040517f00000000000000000000000000000000000000000000000000000000000000008152f35b346100bf575f3660031901126100bf5760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b346100bf575f3660031901126100bf5760206040517f00000000000000000000000000000000000000000000000000000000000000008152f35b346100bf575f3660031901126100bf576040516040810181811067ffffffffffffffff82111761028b57604052600a81526040602082019169507974684f7261636c6560b01b83528151928391602083525180918160208501528484015e5f828201840152601f01601f19168101030190f35b634e487b7160e01b5f52604160045260245ffd5b346100bf5760406102b26100d6366102bf565b8151908082526020820152f35b60609060031901126100bf57600435906024356001600160a01b03811681036100bf57906044356001600160a01b03811681036100bf5790565b6080810190811067ffffffffffffffff82111761028b57604052565b9190610364917f0000000000000000000000000000000000000000000000000000000000000000917f0000000000000000000000000000000000000000000000000000000000000000906106f2565b5f6060604051610373816102f9565b8281528260208201528260408201520152604051916396834ad360e01b83527f000000000000000000000000000000000000000000000000000000000000000060048401526080836024816001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000165afa9283156106e7575f93610649575b5060608301805190428210156106265761041491505142610785565b7f0000000000000000000000000000000000000000000000000000000000000000106105bb575b82515f8160070b13908115916105d5575b506105bb576040830192835160030b60131981129081156105ca575b506105bb5767ffffffffffffffff90511692515f0b60ff7f0000000000000000000000000000000000000000000000000000000000000000165f0b0392607f8413607f198512176105a057835f0b5f81135f1461053357507f00000000000000000000000000000000000000000000000000000000000000009360ff1693602660ff8216118015610529575b61051a57610510610507610517966107a5565b60801b916107a5565b17916107b6565b90565b6302950f9560e51b5f5260045ffd5b50602685116104f4565b909350607f1981146105a05760ff905f031660ff7f000000000000000000000000000000000000000000000000000000000000000016019260ff84116105a057602660ff85161180156105b4575b61051a5761051793600190610595906107a5565b9060801b17916107b6565b634e487b7160e01b5f52601160045260245ffd5b505f610581565b636ba1efb560e11b5f5260045ffd5b600c9150135f610468565b905061271061061e67ffffffffffffffff6020870151169267ffffffffffffffff7f00000000000000000000000000000000000000000000000000000000000000009116610792565b04105f61044c565b50610634603c914290610785565b111561043b57636ba1efb560e11b5f5260045ffd5b90925060803d6080116106e0575b601f8101601f1916820167ffffffffffffffff81118382101761028b576080918391604052810103126100bf5760405190610691826102f9565b80518060070b81036100bf578252602081015167ffffffffffffffff811681036100bf5760208301526040810151908160030b82036100bf57606091604084015201516060820152915f6103f8565b503d610657565b6040513d5f823e3d90fd5b906001600160a01b038091949394169116918183148061076a575b610762576001600160a01b03168214908161074f575b50610748576001600160a01b0392506304ca22af60e41b5f526004521660245260445ffd5b5050600190565b90506001600160a01b038316145f610723565b505050505f90565b506001600160a01b0381166001600160a01b0385161461070d565b919082039182116105a057565b818102929181159184041417156105a057565b60ff16604d81116105a057600a0a90565b92608083901c926fffffffffffffffffffffffffffffffff1690156107ea576105179392916107e491610792565b916107fa565b61051793916107f891610792565b905b81810292918115828504821417830215610815575050900490565b82905f1981840985811086019003920990825f03831692818111156108805783900480600302600218808202600203028082026002030280820260020302808202600203028082026002030280910260020302936001848483030494805f0304019211900302170290565b63ae47f7025f526004601cfdfea26469706673582212202dbc8568782388c2490130f30ac140b067cc0e71fa9fd22e23a46d0bb22c300864736f6c634300081c0033", "args_data": "0x0000000000000000000000004305fb66699c3b2702d4d05cf36551390a4c69c60000000000000000000000009d39a5de30e57443bff2a8307a4256c8797a34970000000000000000000000000000000000000000000000000000000000000348ca3ba9a619a4b3755c10ac7d5e760275aa95e9823d38a84fedd416856cdba37c000000000000000000000000000000000000000000000000000000000000003c0000000000000000000000000000000000000000000000000000000000000032", "tx_hash": "", "args": [], "data": "", "artifact_path": "PythOracle.sol", "artifact_full_path": "PythOracle.sol:PythOracle"}