{"address": "0x275423F55C872b53F9b0767D830F173fb11ab6C5", "abi": [{"inputs": [], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_PLUGINS_PER_ACCOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PLUGIN_CALL_GAS_LIMIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "plugin", "type": "address"}], "name": "addPlugin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "asset", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "assetRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "basketManager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bitFlag", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "cancelDepositRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "cancelRedeemRequest", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "claimFallbackAssets", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "claimFallbackShares", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "claimableDepositRequest", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}], "name": "claimableFallbackAssets", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}], "name": "claimableFallbackShares", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "claimableRedeemRequest", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "convertToAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "convertToShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "deposit", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "deposit", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "fallbackDepositTriggered", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "fallback<PERSON><PERSON><PERSON>Triggered", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "fulfillDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "fulfillRedeem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getAssets", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "getDepositRequest", "outputs": [{"components": [{"internalType": "uint256", "name": "totalDepositAssets", "type": "uint256"}, {"internalType": "uint256", "name": "fulfilledShares", "type": "uint256"}, {"internalType": "bool", "name": "fallbackTriggered", "type": "bool"}], "internalType": "struct BasketToken.DepositRequestView", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "getRedeemRequest", "outputs": [{"components": [{"internalType": "uint256", "name": "totalRedeemShares", "type": "uint256"}, {"internalType": "uint256", "name": "fulfilledAssets", "type": "uint256"}, {"internalType": "bool", "name": "fallbackTriggered", "type": "bool"}], "internalType": "struct BasketToken.RedeemRequestView", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTargetWeights", "outputs": [{"internalType": "uint64[]", "name": "", "type": "uint64[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "harvestManagementFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "plugin", "type": "address"}], "name": "hasPlugin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IERC20", "name": "asset_", "type": "address"}, {"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint256", "name": "bitFlag_", "type": "uint256"}, {"internalType": "address", "name": "strategy_", "type": "address"}, {"internalType": "address", "name": "assetRegistry_", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}], "name": "lastDepositRequestId", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastManagementFeeHarvestTimestamp", "outputs": [{"internalType": "uint40", "name": "", "type": "uint40"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}], "name": "lastRedeemRequestId", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}], "name": "maxDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}], "name": "maxMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}], "name": "max<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}], "name": "max<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "name": "multicall", "outputs": [{"internalType": "bytes[]", "name": "results", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextDepositRequestId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextRedeemRequestId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "pendingDepositRequest", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "pendingRedeemRequest", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "permit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "permit2", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "pluginAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "plugin", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "pluginBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "plugins", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "pluginsCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "feeBps", "type": "uint16"}, {"internalType": "address", "name": "feeCollector", "type": "address"}], "name": "prepareForRebalance", "outputs": [{"internalType": "uint256", "name": "pendingDeposits", "type": "uint256"}, {"internalType": "uint256", "name": "pendingShares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "previewDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "previewMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "previewRedeem", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "previewWithdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}], "name": "proRataRedeem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "redeem", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "removeAllPlugins", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "plugin", "type": "address"}], "name": "removePlugin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "requestDeposit", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "requestRedeem", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "bitFlag_", "type": "uint256"}], "name": "setBitFlag", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setOperator", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "share", "outputs": [{"internalType": "address", "name": "shareTokenAddress", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "strategy", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalPendingDeposits", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalPendingRedemptions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldBitFlag", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newBitFlag", "type": "uint256"}], "name": "BitFlagUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "DepositFallbackTriggered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "DepositFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "controller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "DepositRequest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "depositRequestId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "pendingDeposits", "type": "uint256"}], "name": "DepositRequestQueued", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EIP712DomainChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "ManagementFeeHarvested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "controller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "OperatorSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "address", "name": "plugin", "type": "address"}], "name": "PluginAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "address", "name": "plugin", "type": "address"}], "name": "PluginRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "RedeemFallbackTriggered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "RedeemFulfilled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "controller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "RedeemRequest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "redeemRequestId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "pendingShares", "type": "uint256"}], "name": "RedeemRequestQueued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "Withdraw", "type": "event"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [], "name": "AssetPaused", "type": "error"}, {"inputs": [], "name": "CannotFulfillWithZeroAssets", "type": "error"}, {"inputs": [], "name": "CannotFulfillWithZeroShares", "type": "error"}, {"inputs": [], "name": "DepositRequestAlreadyFulfilled", "type": "error"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "ERC2612ExpiredSignature", "type": "error"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC2612InvalidSigner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "name": "ERC4626ExceededMaxDeposit", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "name": "ERC4626ExceededMaxMint", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "name": "ERC4626ExceededMaxRedeem", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "name": "ERC4626ExceededMaxWithdraw", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "IndexOutOfBounds", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "name": "InvalidAccountNonce", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "InvalidManagementFee", "type": "error"}, {"inputs": [], "name": "InvalidPluginAddress", "type": "error"}, {"inputs": [], "name": "InvalidTokenInPlugin", "type": "error"}, {"inputs": [], "name": "MustClaimFullAmount", "type": "error"}, {"inputs": [], "name": "MustClaimOutstandingDeposit", "type": "error"}, {"inputs": [], "name": "MustClaimOutstandingRedeem", "type": "error"}, {"inputs": [], "name": "NotAuthorizedOperator", "type": "error"}, {"inputs": [], "name": "NotBasketManager", "type": "error"}, {"inputs": [], "name": "NotFeeCollector", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "PluginAlreadyAdded", "type": "error"}, {"inputs": [], "name": "PluginNotFound", "type": "error"}, {"inputs": [], "name": "PluginsLimitReachedForAccount", "type": "error"}, {"inputs": [], "name": "PreviousDepositRequestNotFulfilled", "type": "error"}, {"inputs": [], "name": "PreviousRedeemRequestNotFulfilled", "type": "error"}, {"inputs": [], "name": "RedeemRequestAlreadyFulfilled", "type": "error"}, {"inputs": [], "name": "ReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "UnsafeCast", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}, {"inputs": [], "name": "ZeroAmount", "type": "error"}, {"inputs": [], "name": "ZeroClaimableFallbackAssets", "type": "error"}, {"inputs": [], "name": "ZeroClaimableFallbackShares", "type": "error"}, {"inputs": [], "name": "ZeroPendingDeposits", "type": "error"}, {"inputs": [], "name": "ZeroPendingRedeems", "type": "error"}, {"inputs": [], "name": "ZeroPluginsLimit", "type": "error"}], "bytecode": "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", "args_data": "0x", "tx_hash": "", "args": [], "data": "", "artifact_path": "BasketToken.sol", "artifact_full_path": "BasketToken.sol:BasketToken"}