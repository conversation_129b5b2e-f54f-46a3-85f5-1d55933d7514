{"address": "0xf33958E73B8A1325af667598154948557C3Dd0ea", "abi": [{"inputs": [{"internalType": "contract IERC4626", "name": "_initialVault", "type": "address"}, {"internalType": "address", "name": "_targetAsset", "type": "address"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "base", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quote", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "vaults", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ChainTooLong", "type": "error"}, {"inputs": [], "name": "Invalid<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [{"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "PriceOracle_NotSupported", "type": "error"}, {"inputs": [], "name": "TargetAssetNotReached", "type": "error"}], "bytecode": "0x60c0806040526040816106f7803803809161001a82856101e0565b83398101031261019d5780516001600160a01b038116919082900361019d5760206100459101610203565b5f91805b600a84106100b2575b50909180159081156100a7575b506100985760805260a0526040516104df9081610218823960805181818161012201526102b0015260a051818181609f015261028e0152f35b63c253bd8960e01b5f5260045ffd5b600a9150145f61005f565b6001600160a01b03169283156101d1575f54680100000000000000008110156101bd5760018101805f558110156101a9575f80527f290decd9548b62a8d60345a988386fc84ba6bc95484008f6362f93160ef3e5630180546001600160a01b031916851790556040516338d52e0f60e01b815293602090859060049082905afa5f948161016a575b5061014e5763af97b84360e01b5f5260045ffd5b600101926001600160a01b038381169082160361004957610052565b9094506020813d82116101a1575b81610185602093836101e0565b8101031261019d5761019690610203565b935f61013a565b5f80fd5b3d9150610178565b634e487b7160e01b5f52603260045260245ffd5b634e487b7160e01b5f52604160045260245ffd5b630104068f60e71b5f5260045ffd5b601f909101601f19168101906001600160401b038211908210176101bd57604052565b51906001600160a01b038216820361019d5756fe60806040526004361015610011575f80fd5b5f3560e01c80630579e61f146101e057806306fdde03146101465780635001f3b5146101035780638c64ea4a146100c3578063999b93af146100805763ae68676c1461005b575f80fd5b3461007c57602061007461006e36610200565b91610286565b604051908152f35b5f80fd5b3461007c575f36600319011261007c5760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b3461007c57602036600319011261007c576004355f5481101561007c576001600160a01b036100f360209261025c565b90549060031b1c16604051908152f35b3461007c575f36600319011261007c5760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b3461007c575f36600319011261007c576040516040810181811067ffffffffffffffff8211176101cc5760405260148152604060208201917f436861696e6564455243343632364f7261636c6500000000000000000000000083528151928391602083525180918160208501528484015e5f828201840152601f01601f19168101030190f35b634e487b7160e01b5f52604160045260245ffd5b3461007c5760406101f361006e36610200565b8151908082526020820152f35b606090600319011261007c57600435906024356001600160a01b038116810361007c57906044356001600160a01b038116810361007c5790565b90601f8019910116810190811067ffffffffffffffff8211176101cc57604052565b5f54811015610272575f805260205f2001905f90565b634e487b7160e01b5f52603260045260245ffd5b91906102d5917f0000000000000000000000000000000000000000000000000000000000000000917f000000000000000000000000000000000000000000000000000000000000000090610416565b908015610410575f549161038157905f905b8082106102f357505090565b909160206001600160a01b036103088561025c565b90549060031b1c16916024604051809481936303d1689d60e11b835260048301525afa8015610376575f90610344575b600191509201906102e7565b506020813d821161036e575b8161035d6020938361023a565b8101031261007c5760019051610338565b3d9150610350565b6040513d5f823e3d90fd5b90805b61038c575090565b5f19019060206001600160a01b036103a38461025c565b90549060031b1c16916024604051809481936363737ac960e11b835260048301525afa80156103765782915f916103dc575b5091610384565b9150506020813d8211610408575b816103f76020938361023a565b8101031261007c578190515f6103d5565b3d91506103ea565b50505f90565b906001600160a01b038091949394169116918183148061048e575b610486576001600160a01b031682149081610473575b5061046c576001600160a01b0392506304ca22af60e41b5f526004521660245260445ffd5b5050600190565b90506001600160a01b038316145f610447565b505050505f90565b506001600160a01b0381166001600160a01b0385161461043156fea26469706673582212202eb9a8159b9bc81eac6bc659e7d0edfd63ea36d3b54e8dd16a4658d8b3975b1564736f6c634300081c0033", "args_data": "0x00000000000000000000000081f78def7a3a8b0f6aaba69925efc69e70239d95000000000000000000000000dc035d45d973e3ec169d2276ddab16f1e407384f", "tx_hash": "", "args": [], "data": "", "artifact_path": "ChainedERC4626Oracle.sol", "artifact_full_path": "ChainedERC4626Oracle.sol:ChainedERC4626Oracle"}