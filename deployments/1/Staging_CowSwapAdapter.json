{"address": "0x1db6482f07B223fad08E8Ec4648024601b2dA7Ad", "abi": [{"inputs": [{"internalType": "address", "name": "cloneImplementation_", "type": "address"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "cloneImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "sellToken", "type": "address"}, {"internalType": "address", "name": "buyToken", "type": "address"}, {"internalType": "uint256", "name": "sellAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"components": [{"internalType": "address", "name": "basket", "type": "address"}, {"internalType": "uint96", "name": "tradeOwnership", "type": "uint96"}], "internalType": "struct BasketTradeOwnership[]", "name": "basketTradeOwnership", "type": "tuple[]"}], "internalType": "struct ExternalTrade[]", "name": "externalTrades", "type": "tuple[]"}], "name": "completeTokenSwap", "outputs": [{"internalType": "uint256[2][]", "name": "claimedAmounts", "type": "uint256[2][]"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "sellToken", "type": "address"}, {"internalType": "address", "name": "buyToken", "type": "address"}, {"internalType": "uint256", "name": "sellAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"components": [{"internalType": "address", "name": "basket", "type": "address"}, {"internalType": "uint96", "name": "tradeOwnership", "type": "uint96"}], "internalType": "struct BasketTradeOwnership[]", "name": "basketTradeOwnership", "type": "tuple[]"}], "internalType": "struct ExternalTrade[]", "name": "externalTrades", "type": "tuple[]"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "executeTokenSwap", "outputs": [], "stateMutability": "payable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sellToken", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "sellAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "buyAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint32", "name": "validTo", "type": "uint32"}, {"indexed": false, "internalType": "address", "name": "swapContract", "type": "address"}], "name": "OrderCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sellToken", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "claimedSellAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "claimedBuyAmount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "swapContract", "type": "address"}], "name": "TokenSwapCompleted", "type": "event"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}], "bytecode": "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", "args_data": "0x0000000000000000000000009d677ed832d57588217593c8b4e3530825788d0d", "tx_hash": "", "args": [], "data": "", "artifact_path": "CoWSwapAdapter.sol", "artifact_full_path": "CoWSwapAdapter.sol:CoWSwapAdapter"}