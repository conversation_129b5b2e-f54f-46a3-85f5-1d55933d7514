{"address": "0x36230d01F8516CBBd3f787F319356C91B6D5022C", "abi": [{"inputs": [{"internalType": "address", "name": "_pool", "type": "address"}, {"internalType": "address", "name": "_base", "type": "address"}, {"internalType": "address", "name": "_quote", "type": "address"}, {"internalType": "uint256", "name": "_priceOracleIndex", "type": "uint256"}, {"internalType": "bool", "name": "isBaseUnderlying", "type": "bool"}, {"internalType": "bool", "name": "isQuoteUnderlying", "type": "bool"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "base", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pool", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracleIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quote", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "BaseAssetMismatch", "type": "error"}, {"inputs": [], "name": "PriceOracle_InvalidConfiguration", "type": "error"}, {"inputs": [{"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "PriceOracle_NotSupported", "type": "error"}, {"inputs": [], "name": "PriceOracle_Overflow", "type": "error"}, {"inputs": [], "name": "QuoteAssetMismatch", "type": "error"}], "bytecode": "0x6101208060405260c081610b34803803809161001b82856103e2565b8339810103126102b25761002e81610419565b9061003b60208201610419565b61004760408301610419565b9160608101519061006660a061005f6080840161042d565b920161042d565b6001600160a01b0386169081156103d3576001600160a01b03851680156103d3576001600160a01b0387169384156103d3575f1986036103c2576001905b6040519163c661065760e01b83526004830152602082602481885afa9182156102be575f92610381575b5060206024956040519687809263c661065760e01b82525f60048301525afa9485156102be575f95610345575b5015610328576040516338d52e0f60e01b815290602090829060049082906001600160a01b03165afa9081156102be575f916102ee575b506001600160a01b0316036102df575b156102c9576040516338d52e0f60e01b815290602090829060049082906001600160a01b03165afa9081156102be575f91610280575b506001600160a01b0316036102715760ff925b6101948361043a565b9261019e8261043a565b9560805260a05260c05260e0521660120160ff811161025d57602660ff8316118015610250575b610241576101d56101de916104f1565b60801b916104f1565b17610100526040516106319081610503823960805181818161016a0152818161033301526103fd015260a05181818160ed01526102ce015260c05181818160aa01526102ac015260e05181818161012801526102f5015261010051816103720152f35b6302950f9560e51b5f5260045ffd5b50602660ff8216116101c5565b634e487b7160e01b5f52601160045260245ffd5b638be8341d60e01b5f5260045ffd5b90506020813d6020116102b6575b8161029b602093836103e2565b810103126102b2576102ac90610419565b5f610178565b5f80fd5b3d915061028e565b6040513d5f823e3d90fd5b6001600160a01b0316036102715760ff9261018b565b6380d82a6b60e01b5f5260045ffd5b90506020813d602011610320575b81610309602093836103e2565b810103126102b25761031a90610419565b5f610132565b3d91506102fc565b6001600160a01b031614610142576380d82a6b60e01b5f5260045ffd5b9094506020813d602011610379575b81610361602093836103e2565b810103126102b25761037290610419565b935f6100fb565b3d9150610354565b9491506020853d6020116103ba575b8161039d602093836103e2565b810103126102b25760206103b2602496610419565b9295506100ce565b3d9150610390565b6001860180871161025d57906100a4565b6301a4c16560e21b5f5260045ffd5b601f909101601f19168101906001600160401b0382119082101761040557604052565b634e487b7160e01b5f52604160045260245ffd5b51906001600160a01b03821682036102b257565b519081151582036102b257565b63ffffffff6001600160a01b03821611156104d8575f8091604051602081019063313ce56760e01b8252600481526104736024826103e2565b51915afa3d156104e9573d906001600160401b03821161040557604051916104a5601f8201601f1916602001846103e2565b82523d5f602084013e5b806104de575b156104d8576020818051810103126102b2576020015160ff811681036102b25790565b50601290565b5060208151146104b5565b6060906104af565b60ff16604d811161025d57600a0a9056fe60806040526004361015610011575f80fd5b5f3560e01c80630579e61f1461022857806306fdde031461018e57806316f0115b1461014b5780631ecac338146101115780635001f3b5146100ce578063999b93af1461008b5763ae68676c14610066575f80fd5b3461008757602061007f61007936610248565b916102a4565b604051908152f35b5f80fd5b34610087575f3660031901126100875760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b34610087575f3660031901126100875760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b34610087575f3660031901126100875760206040517f00000000000000000000000000000000000000000000000000000000000000008152f35b34610087575f3660031901126100875760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b34610087575f366003190112610087576040516040810181811067ffffffffffffffff8211176102145760405260188152604060208201917f4375727665454d414f7261636c65556e6465726c79696e67000000000000000083528151928391602083525180918160208501528484015e5f828201840152601f01601f19168101030190f35b634e487b7160e01b5f52604160045260245ffd5b3461008757604061023b61007936610248565b8151908082526020820152f35b606090600319011261008757600435906024356001600160a01b038116810361008757906044356001600160a01b03811681036100875790565b90601f8019910116810190811067ffffffffffffffff82111761021457604052565b91906102f3917f0000000000000000000000000000000000000000000000000000000000000000917f00000000000000000000000000000000000000000000000000000000000000009061046a565b7f00000000000000000000000000000000000000000000000000000000000000005f1981036103d957506040516386fc88d360e01b8152916020836004817f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03165afa80156103ce575f9061039a575b61039793505b7f000000000000000000000000000000000000000000000000000000000000000091610524565b90565b506020833d6020116103c6575b816103b460209383610282565b8101031261008757610397925161036a565b3d91506103a7565b6040513d5f823e3d90fd5b9160405192636872765360e01b845260048401526020836024816001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000165afa80156103ce575f90610436575b6103979350610370565b506020833d602011610462575b8161045060209383610282565b8101031261008757610397925161042c565b3d9150610443565b906001600160a01b03809194939416911691818314806104e2575b6104da576001600160a01b0316821490816104c7575b506104c0576001600160a01b0392506304ca22af60e41b5f526004521660245260445ffd5b5050600190565b90506001600160a01b038316145f61049b565b505050505f90565b506001600160a01b0381166001600160a01b03851614610485565b8181029291811591840414171561051057565b634e487b7160e01b5f52601160045260245ffd5b92608083901c926fffffffffffffffffffffffffffffffff16901561055857610397939291610552916104fd565b91610568565b6103979391610566916104fd565b905b81810292918115828504821417830215610583575050900490565b82905f1981840985811086019003920990825f03831692818111156105ee5783900480600302600218808202600203028082026002030280820260020302808202600203028082026002030280910260020302936001848483030494805f0304019211900302170290565b63ae47f7025f526004601cfdfea264697066735822122022f45ca59df72e22944b284375f38f7e90a63470bfa6b44144b46998a8416ec964736f6c634300081c0033", "args_data": "0x0000000000000000000000003bd1017929b43c1414be2aca39892590fba4d6e20000000000000000000000004c9edd5852cd905f086c759e8383e09bff1e68b3000000000000000000000000cacd6fd266af91b8aed52accc382b4e165586e29000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "tx_hash": "", "args": [], "data": "", "artifact_path": "CurveEMAOracleUnderlying.sol", "artifact_full_path": "CurveEMAOracleUnderlying.sol:CurveEMAOracleUnderlying"}