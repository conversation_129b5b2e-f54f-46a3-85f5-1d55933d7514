{"address": "0x01Dc22D46d8CF6100AB91fBb4e614c52f8cc726E", "abi": [{"inputs": [{"internalType": "contract IERC4626", "name": "_vault", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "base", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "getQuotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quote", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "base", "type": "address"}, {"internalType": "address", "name": "quote", "type": "address"}], "name": "PriceOracle_NotSupported", "type": "error"}], "bytecode": "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", "args_data": "0x", "tx_hash": "", "args": [], "data": "", "artifact_path": "ERC4626Oracle.sol", "artifact_full_path": "ERC4626Oracle.sol:ERC4626Oracle"}