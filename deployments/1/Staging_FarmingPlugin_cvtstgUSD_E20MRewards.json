{"address": "0x27BdAAdfDc0c3E39ad38C86f2f1774B51E4D237e", "abi": [{"inputs": [{"internalType": "contract IERC20Plugins", "name": "farmableToken_", "type": "address"}, {"internalType": "contract IERC20", "name": "rewardsToken_", "type": "address"}, {"internalType": "address", "name": "owner_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "REWARDS_TOKEN", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKEN", "outputs": [{"internalType": "contract IERC20Plugins", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "distributor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "farmInfo", "outputs": [{"components": [{"internalType": "uint40", "name": "finished", "type": "uint40"}, {"internalType": "uint32", "name": "duration", "type": "uint32"}, {"internalType": "uint184", "name": "reward", "type": "uint184"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}], "internalType": "struct FarmAccounting.Info", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "farmed", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IERC20", "name": "token_", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "rescueFunds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "distributor_", "type": "address"}], "name": "setDistributor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "period", "type": "uint256"}], "name": "startFarming", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stopFarming", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "updateBalances", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newDistributor", "type": "address"}], "name": "DistributorChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "address", "name": "reward", "type": "address"}], "name": "FarmCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "RewardUpdated", "type": "event"}, {"inputs": [], "name": "AccessDenied", "type": "error"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "Du<PERSON><PERSON><PERSON><PERSON><PERSON>ge", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "NotDistributor", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "SafeTransferFailed", "type": "error"}, {"inputs": [], "name": "SafeTransferFromFailed", "type": "error"}, {"inputs": [], "name": "ZeroDistributorAddress", "type": "error"}, {"inputs": [], "name": "ZeroDuration", "type": "error"}, {"inputs": [], "name": "ZeroFarmableTokenAddress", "type": "error"}, {"inputs": [], "name": "ZeroRewardsTokenAddress", "type": "error"}], "bytecode": "0x60c03461018a57601f61108e38819003918201601f19168301916001600160401b0383118484101761018e5780849260609460405283398101031261018a5780516001600160a01b038116919082810361018a576020820151916001600160a01b0383169081840361018a57604001516001600160a01b038116929083900361018a576080528115610177575f80546001600160a01b031981168417825560405193916001600160a01b03909116907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09080a38315610168578015610159577f6bff9ddd187ef283e9c7726f406ab27bcc3719a41b6bee3585c7447183cffcec9360409360a05282526020820152a1604051610eeb90816101a382396080518181816101ee0152818161024d0152818161089a01526109f3015260a051818181610160015281816104950152818161076a01526109140152f35b6330c1035b60e21b5f5260045ffd5b63d0af4eb760e01b5f5260045ffd5b631e4fbdf760e01b5f525f60045260245ffd5b5f80fd5b634e487b7160e01b5f52604160045260245ffdfe6080806040526004361015610012575f80fd5b5f3560e01c90816318160ddd14610bed575080631bfa4c0414610b2b5780631d49d66c14610a845780634216f972146109a45780634e71d92d14610869578063702612221461064a578063715018a6146105e657806375619ab51461056857806378e3214f146103ac5780637a1f1aa91461021257806382bfefc8146101cf5780638da5cb5b146101aa578063bfe1092814610184578063c5601072146101415763f2fde38b146100c1575f80fd5b3461013d57602036600319011261013d576001600160a01b036100e2610c07565b6100ea610d14565b16801561012a576001600160a01b035f548282198216175f55167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e05f80a3005b631e4fbdf760e01b5f525f60045260245ffd5b5f80fd5b3461013d575f36600319011261013d5760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b3461013d575f36600319011261013d5760206001600160a01b0360015416604051908152f35b3461013d575f36600319011261013d5760206001600160a01b035f5416604051908152f35b3461013d575f36600319011261013d5760206040516001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168152f35b3461013d57606036600319011261013d5761022b610c07565b6024356001600160a01b03811680910361013d57604435916001600160a01b037f000000000000000000000000000000000000000000000000000000000000000016330361039d5761027b610c91565b906001600160a01b03610292602084015193610d47565b91169283159281159486151580610393575b6102dd575b505050506102c9575b6102b857005b6102c490600254610d3a565b600255005b6102d582600254610c5b565b6002556102b2565b6102f685948615958661038c575b61036c575b88610dfb565b9361034c575b50841561030a575b806102a9565b6003915f520160205260405f2080545f83820193841291129080158216911516176103385755838080610304565b634e487b7160e01b5f52601160045260245ffd5b5f526003810160205260405f20610364848254610e0e565b9055866102fc565b4264ffffffffff16602882901b64ffffffffff19161760028501556102f0565b50876102eb565b50828114156102a4565b634ca8886760e01b5f5260045ffd5b3461013d57604036600319011261013d576004356001600160a01b03811680820361013d57602435906001600160a01b036001541690813303610559578061048b57505090506001600160a01b0360015416814710610474575f80809381935af13d1561046f573d67ffffffffffffffff811161045b576040519061043b601f8201601f191660200183610c39565b81525f60203d92013e5b1561044c57005b63d6bda27560e01b5f5260045ffd5b634e487b7160e01b5f52604160045260245ffd5b610445565b504763cf47918160e01b5f5260045260245260445ffd5b6001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168091146104c9575b506104c792610cb0565b005b6020602491604051928380926370a0823160e01b82523060048301525afa90811561054e575f9161051c575b5061050283600454610c5b565b1161050d57836104bd565b63356680b760e01b5f5260045ffd5b90506020813d602011610546575b8161053760209383610c39565b8101031261013d5751846104f5565b3d915061052a565b6040513d5f823e3d90fd5b63385296d560e01b5f5260045ffd5b3461013d57602036600319011261013d576001600160a01b03610589610c07565b610591610d14565b1680156105d7577fe37acc13f5ed9d0cc83c2842e093fe5a494d5b8fb5b1db06356b327081832f526020604051838152a16001600160a01b031960015416176001555f80f35b637170207960e01b5f5260045ffd5b3461013d575f36600319011261013d576105fe610d14565b5f805473ffffffffffffffffffffffffffffffffffffffff19811682556001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a3005b3461013d57604036600319011261013d576004356024356001600160a01b036001541633036105595761067b610c91565b6106aa61068c602083015192610d47565b4264ffffffffff1660289190911b64ffffffffff1916176002830155565b82821561085a5763ffffffff831161084b5781549264ffffffffff84169260018101948554948042106107fc575b50506d04ee2d6d415b85acef810000000083116107ed5764ffffffffff6040947f3efe2b1ade87153c913a322f09a35c930d7fd699770b8d7cdd294e1debf6e9e49661073086846107298842610c5b565b1693610c5b565b90551668ffffffffffffffffff19604885901b1668ffffffff0000000000602885901b161717905582519182526020820152a160205f60647f000000000000000000000000000000000000000000000000000000000000000093604051906323b872dd60e01b8252336004830152306024830152604482015282855af190816107cb575b50156107bc57005b63f405907160e01b5f5260045ffd5b90503d156107e5575060015f5114601f3d11165b816107b4565b3b15156107df565b630625040160e01b5f5260045ffd5b61084392945090670de0b6b3a764000061083364ffffffffff61082c61083d9563ffffffff8660281c1690610dc1565b1687610e5e565b049060481c610d3a565b86610c5b565b9186806106d8565b63426a107360e01b5f5260045ffd5b63346ab43760e11b5f5260045ffd5b3461013d575f36600319011261013d5760405163c125563560e01b81523060048201523360248201526020816044817f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03165afa90811561054e575f91610972575b506108db610c91565b906108ea602083015192610d47565b6108f981833360028701610e26565b928315159283610938575b50505061090d57005b6104c790337f0000000000000000000000000000000000000000000000000000000000000000610cb0565b60019261094491610dfb565b6001600160a01b0333165f526003820160205260405f205501610968838254610d3a565b9055828080610904565b90506020813d60201161099c575b8161098d60209383610c39565b8101031261013d5751816108d2565b3d9150610980565b3461013d57602036600319011261013d576109bd610c07565b60405163c125563560e01b81523060048201526001600160a01b0382166024820152602081806044810103816001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000165afa90811561054e575f91610a51575b6020610a498484610a32610c91565b91610a436002868501510193610d47565b92610e26565b604051908152f35b90506020813d602011610a7c575b81610a6c60209383610c39565b8101031261013d57516020610a23565b3d9150610a5f565b3461013d575f36600319011261013d575f6060604051610aa381610c1d565b82815282602082015282604082015201526080604051610ac281610c1d565b6003549076ffffffffffffffffffffffffffffffffffffffffffffff64ffffffffff83169283835263ffffffff60208401818360281c168152604085019260481c8352606060045495019485526040519586525116602085015251166040830152516060820152f35b3461013d575f36600319011261013d576001600160a01b0360015416330361055957610b55610c91565b610b6661068c602083015192610d47565b610b998154670de0b6b3a764000061083364ffffffffff610b9263ffffffff8560281c16828616610dc1565b1685610e5e565b9060018101610ba9838254610d3a565b90554264ffffffffff169055604080515f80825260208201527f3efe2b1ade87153c913a322f09a35c930d7fd699770b8d7cdd294e1debf6e9e49190a18061090d57005b3461013d575f36600319011261013d576020906002548152f35b600435906001600160a01b038216820361013d57565b6080810190811067ffffffffffffffff82111761045b57604052565b90601f8019910116810190811067ffffffffffffffff82111761045b57604052565b9190820180921161033857565b604051906040820182811067ffffffffffffffff82111761045b576040525f6020838281520152565b610c99610c68565b50610ca2610c68565b600381526003602082015290565b9160446020925f926040519163a9059cbb60e01b83526004830152602482015282855af19081610cf2575b5015610ce357565b63fb7f507960e01b5f5260045ffd5b90503d15610d0c575060015f5114601f3d11165b5f610cdb565b3b1515610d06565b6001600160a01b035f54163303610d2757565b63118cdaa760e01b5f523360045260245ffd5b9190820391821161033857565b602081016002815101549064ffffffffff82169160281c92824203610d6d575b50505090565b610d75610c68565b5051600314610d9257634e487b7160e01b5f52605160045260245ffd5b600254908115610d6757610db892610db391610dac610c68565b5051610e5e565b610ddd565b015f8080610d67565b9064ffffffffff8091169116039064ffffffffff821161033857565b8115610de7570490565b634e487b7160e01b5f52601260045260245ffd5b8181029291811591840414171561033857565b81810392915f13801582851316918412161761033857565b6001906001600160a01b03610e47670de0b6b3a764000096610e5a96610dfb565b93165f520160205260405f205490610e0e565b0490565b91905f92549064ffffffffff821663ffffffff8360281c169283610e825750505050565b610eb294955090670de0b6b3a7640000929160481c91818082109118028118908042108142180218030202610ddd565b9056fea26469706673582212203ee9a4ca9d67540bb951e265d1c581ac723462f7158b9c4bee528f6cc2f1460764736f6c634300081c0033", "args_data": "0x", "tx_hash": "", "args": [], "data": "", "artifact_path": "FarmingPlugin.sol", "artifact_full_path": "FarmingPlugin.sol:FarmingPlugin"}