{"address": "0x3F0de00c4332121764837c7bbd30bB9Cb66b0A61", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "basketManager", "type": "address"}, {"internalType": "address", "name": "treasury", "type": "address"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "basketToken", "type": "address"}], "name": "basketTokenSponsorSplits", "outputs": [{"internalType": "uint16", "name": "sponsorSplit", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "basketToken", "type": "address"}], "name": "basketTokenSponsors", "outputs": [{"internalType": "address", "name": "sponsor", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "basketToken", "type": "address"}], "name": "claimSponsorFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "basketToken", "type": "address"}], "name": "claimTreasuryFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "basketToken", "type": "address"}], "name": "claimableSponsorFees", "outputs": [{"internalType": "uint256", "name": "claimableFees", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "basketToken", "type": "address"}], "name": "claimableTreasuryFees", "outputs": [{"internalType": "uint256", "name": "claimableFees", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "notifyHarvestFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "protocolTreasury", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "rescue", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "treasury", "type": "address"}], "name": "setProtocolTreasury", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "basketToken", "type": "address"}, {"internalType": "address", "name": "sponsor", "type": "address"}], "name": "setSponsor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "basketToken", "type": "address"}, {"internalType": "uint16", "name": "sponsorSplit", "type": "uint16"}], "name": "setSponsorSplit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "basketToken", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sponsor", "type": "address"}], "name": "SponsorSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "basketToken", "type": "address"}, {"indexed": false, "internalType": "uint16", "name": "sponsorSplit", "type": "uint16"}], "name": "SponsorSplitSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "treasury", "type": "address"}], "name": "TreasurySet", "type": "event"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EthTransferFailed", "type": "error"}, {"inputs": [], "name": "InsufficientFundsToRescue", "type": "error"}, {"inputs": [], "name": "NoSponsor", "type": "error"}, {"inputs": [], "name": "NotBasketToken", "type": "error"}, {"inputs": [], "name": "NotTreasury", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "SponsorSplitTooHigh", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}, {"inputs": [], "name": "ZeroEthTransfer", "type": "error"}, {"inputs": [], "name": "ZeroTokenTransfer", "type": "error"}], "bytecode": "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", "args_data": "0x000000000000000000000000c8ede693e4b8cdf4f3c42bf141d9054050e5a728000000000000000000000000beccf8486856476e9cd8ad6fad80fb7c17a15da1000000000000000000000000c8ede693e4b8cdf4f3c42bf141d9054050e5a728", "tx_hash": "", "args": [], "data": "", "artifact_path": "FeeCollector.sol", "artifact_full_path": "FeeCollector.sol:FeeCollector"}