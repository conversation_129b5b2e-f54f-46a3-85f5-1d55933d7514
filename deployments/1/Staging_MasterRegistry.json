{"address": "0x44bb20e5a3cc2cbdfb7520aa76281019723382cb", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "manager", "type": "address"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "registryName", "type": "bytes32"}, {"internalType": "address", "name": "registryAddress", "type": "address"}], "name": "addRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "name": "multicall", "outputs": [{"internalType": "bytes[]", "name": "results", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "registryAddress", "type": "address"}], "name": "resolveAddressToRegistryData", "outputs": [{"internalType": "bytes32", "name": "registryName", "type": "bytes32"}, {"internalType": "uint256", "name": "version", "type": "uint256"}, {"internalType": "bool", "name": "isLatest", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "registryName", "type": "bytes32"}, {"internalType": "uint256", "name": "version", "type": "uint256"}], "name": "resolveNameAndVersionToAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "registryName", "type": "bytes32"}], "name": "resolveNameToAllAddresses", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "registryName", "type": "bytes32"}], "name": "resolveNameToLatestAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "registryName", "type": "bytes32"}, {"internalType": "address", "name": "registryAddress", "type": "address"}], "name": "updateRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "name", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "registryAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "version", "type": "uint256"}], "name": "AddRegistry", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "name", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "registryAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "version", "type": "uint256"}], "name": "UpdateRegistry", "type": "event"}, {"inputs": [], "name": "AddressEmpty", "type": "error"}, {"inputs": [{"internalType": "address", "name": "registryAddress", "type": "address"}], "name": "DuplicateReg<PERSON>ry<PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "NameEmpty", "type": "error"}, {"inputs": [{"internalType": "address", "name": "registryAddress", "type": "address"}], "name": "RegistryAddressNotFound", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "name", "type": "bytes32"}], "name": "RegistryNameFound", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "name", "type": "bytes32"}], "name": "RegistryNameNotFound", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "name", "type": "bytes32"}, {"internalType": "uint256", "name": "version", "type": "uint256"}], "name": "RegistryNameVersionNotFound", "type": "error"}], "bytecode": "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", "args_data": "0x0000000000000000000000007bd578354b0b2f02e656f1bdc0e41a80f860534b0000000000000000000000008842fe65a7db9bb5de6d50e49af19496da09f9b5", "tx_hash": "0x5e6e410de3bfa004e433710dcd3dbdc9e3de0d8f90eb02a49c334ea6652c3b36", "args": ["[0xf67ff842596561726e5374616b696e6744656c656761746500000000000000000000000000000000000000000000000005dcdbf02f29239d1f8d9797e22589a2de1c152f, 0xf67ff8425374616b696e6744656c65676174655265776172647300000000000000000000000000000000000000000000f91e84e2e4f692e6d8f7440639d5c2147f4c06f0, 0xf67ff84253776170416e644c6f636b0000000000000000000000000000000000000000000000000000000000000000009dadf9487737de29ac685d231bb94348a2635cbb, 0xf67ff8424459464952656465656d65720000000000000000000000000000000000000000000000000000000000000000986f38b5b096070ee64b12da762468606c8b0706, 0xf67ff842436f766559464900000000000000000000000000000000000000000000000000000000000000000000000000ff71841eefca78a64421db28060855036765c248, 0xf67ff842436f76655946495265776172647347617567650000000000000000000000000000000000000000000000000048302ba7bcdf2bd59d20f8893c0f11b431a3be24, 0xf67ff842436f7665596561726e4761756765466163746f72790000000000000000000000000000000000000000000000842b22eb2a1c1c54344eddbe6959f787c2d15844, 0xf67ff8424d696e694368656656330000000000000000000000000000000000000000000000000000000000000000000053ead38ca96d562433427f7bbfcac960cc1f2e2d, 0xf67ff842436f7665546f6b656e000000000000000000000000000000000000000000000000000000000000000000000032fb7d6e0cbeb9433772689aa4647828cc7cbba8, 0xf67ff842596561726e34363236526f7574657245787400000000000000000000000000000000000000000000000000003327744318b8b0ee79159b7f742fe0b378f93df8]"], "data": "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"}