{"address": "0x5877f26793c44358B0757617f4e3380C344fF523", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "bitFlag", "type": "uint256"}, {"internalType": "address", "name": "weightStrategy", "type": "address"}], "name": "supportsBitFlag", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "StrategyNotSupported", "type": "error"}], "bytecode": "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", "args_data": "0x0000000000000000000000008842fe65a7db9bb5de6d50e49af19496da09f9b5", "tx_hash": "", "args": [], "data": "", "artifact_path": "StrategyRegistry.sol", "artifact_full_path": "StrategyRegistry.sol:StrategyRegistry"}