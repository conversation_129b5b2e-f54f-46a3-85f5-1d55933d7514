[profile.default]
src = "src"
out = "out"
libs = ["lib", "node_modules", "dependencies"]
fs_permissions = [
    { access = "read", path = "./deployments" },
    { access = "read", path = "./out" },
    { access = "write", path = "./dumpStates" },
]
solc = '0.8.28'
ffi = true
evm_version = "prague"
script_execution_protection = false
optimizer = true
optimizer_runs = 400
via_ir = true
remappings = [
    "@openzeppelin-contracts-5.1.0/=dependencies/@openzeppelin-contracts-5.1.0/",
    "@openzeppelin-contracts-upgradeable-5.1.0/=dependencies/@openzeppelin-contracts-upgradeable-5.1.0/",
    "clones-with-immutable-args-1.1.1/=dependencies/clones-with-immutable-args-1.1.1/",
    "euler-price-oracle-1/=dependencies/euler-price-oracle-1/",
    "farming-3.2.0/=dependencies/farming-3.2.0/",
    "forge-deploy-1/=dependencies/forge-deploy-1/",
    "forge-safe-1/=dependencies/forge-safe-1/",
    "forge-std-1.9.6/=dependencies/forge-std-1.9.6/",
    "lifinance/create3-factory-0/=dependencies/lifinance-create3-factory-0/",
    "solady-0.1.17/=dependencies/solady-0.1.17/",
    "solidity-utils-5.3.0/=dependencies/solidity-utils-5.3.0/",
    "token-plugins-upgradeable-1/=dependencies/token-plugins-upgradeable-1/",
]

[profile.invariant]
match_contract = "Invariant"

[profile.default.fuzz]
max_test_rejects = 1_000_000 # Number of times `vm.assume` can fail
runs = 50

[profile.default.invariant]
show_metrics = true
call_override = false # Override unsafe external calls to perform reentrancy checks
depth = 20            # Number of calls executed in one run
fail_on_revert = true
runs = 20

[profile.invariant.invariant]
depth = 100
runs = 100

[fmt]
bracket_spacing = true
int_types = "long"
line_length = 120
multiline_func_header = "all"
number_underscore = "thousands"
quote_style = "double"
tab_width = 4
wrap_comments = true
sort_imports = true
ignore = ["src/deps/**/*", "src/interfaces/deps/**/*"]

# See more config options https://github.com/foundry-rs/foundry/tree/master/config

[rpc_endpoints]
mainnet = "${MAINNET_RPC_URL}"

[etherscan]
mainnet = { key = "${API_KEY_ETHERSCAN}" }

[dependencies]
forge-std = "1.9.6"
"@openzeppelin-contracts" = "5.1.0"
"@openzeppelin-contracts-upgradeable" = "5.1.0"
clones-with-immutable-args = { version = "1.1.1", git = "https://github.com/wighawag/clones-with-immutable-args.git", rev = "196f1ecc6485c1bf2d41677fa01d3df4927ff9ce" }
token-plugins-upgradeable = { version = "1", git = "https://github.com/Storm-Labs-Inc/token-plugins-upgradeable.git", rev = "439806ce46954563a921059b6d44f792f4375312" }
solidity-utils = { version = "5.3.0", git = "https://github.com/1inch/solidity-utils.git", rev = "2a69e2ea6c7551cfae4731752a579de9564b263e" }
farming = { version = "3.2.0", git = "https://github.com/1inch/farming.git", rev = "b4e59e415352d3233ccd3c339d845a1eb2e1139b" }
euler-price-oracle = { version = "1", git = "https://github.com/Storm-Labs-Inc/euler-price-oracle.git", rev = "511ee550b082870d248a4e50491a6a9042cb011d" }
"lifinance/create3-factory" = { version = "0", git = "https://github.com/lifinance/create3-factory.git", rev = "93b667acda0dafcafb6e193610b421ea003f46a9" }
forge-safe = { version = "1", git = "https://github.com/Storm-Labs-Inc/forge-safe.git", rev = "8175a6a60844b86d36b3089fe08c584121c7658d" }
forge-deploy = { version = "1", git = "https://github.com/Storm-Labs-Inc/forge-deploy.git", rev = "2a0c1987ca8a619fd41a0cc546706ebf38a2d4af" }
solady = "0.1.17"

[soldeer]
recursive_deps = true
remappings_location = "config"
