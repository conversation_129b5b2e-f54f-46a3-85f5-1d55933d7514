{"name": "cove-contracts-core", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"prepare": "husky && pnpm run forge-deploy:build", "clean": "rm -rf cache out crytic-export generated && forge clean", "build": "./forge-deploy gen-deployer && forge build --sizes", "build:slither": "forge clean && forge build --skip */test/** */script/** */generated/** --build-info", "test": "forge test -vvv --gas-report", "coverage": "forge coverage --ir-minimum --report lcov", "slither": "pnpm run build:slither && slither . --ignore-compile", "slither-upgradeability": "for f in $(grep -l -r \"openzeppelin-upgradeable\" src); do c=$(echo \"${f##*/}\" | cut -f 1 -d \".\"); echo \"$c\"; slither-check-upgradeability $f $c; echo; done", "semgrep": "semgrep --config p/smart-contracts --exclude deps/ src/", "deployLocal": "DEPLOYMENT_CONTEXT=1-fork forge script script/Deployments_Staging.s.sol --rpc-url http://localhost:8545 --broadcast --sender 0x8842fe65A7Db9BB5De6d50e49aF19496da09F9b5 -vvv --unlocked && ./forge-deploy sync;", "forge-deploy:clean": "rm -rf deployments/1-fork/*", "forge-deploy:build": "cd dependencies/forge-deploy-1 && cargo build --release && cp target/release/forge-deploy ../../forge-deploy", "lint": "pnpm run lint:sol && pnpm run prettier:check", "lint:fix": "pnpm lint:fix:sol && pnpm run prettier:write", "lint:fix:sol": "forge fmt && pnpm run solhint:src --fix && pnpm run solhint:script --fix && pnpm run solhint:test --fix", "lint:sol": "forge fmt --check && pnpm run solhint:src && pnpm run solhint:script && pnpm run solhint:test", "solhint:src": "npx solhint --noPrompt --config .solhint.json --ignore-path .solhintignore \"src/**/*.sol\"", "solhint:script": "npx solhint --noPrompt --config .solhint.script.json --ignore-path .solhintignore \"script/**/*.sol\"", "solhint:test": "npx solhint --noPrompt --config .solhint.test.json --ignore-path .solhintignore \"test/**/*.sol\"", "prettier:check": "npx prettier --check \"**/*.{json,md,yml,yaml}\"", "prettier:write": "npx prettier --write \"**/*.{json,md,yml,yaml}\""}, "lint-staged": {"src/!(deps/**/*|interfaces/deps/**/*)/**/*.sol": ["forge fmt", "npx solhint --fix --noPrompt --config .solhint.json --ignore-path .solhintignore"], "script/**/*.sol": ["forge fmt", "npx solhint --fix --noPrompt --config .solhint.script.json --ignore-path .solhintignore"], "test/**/*.sol": ["forge fmt", "npx solhint --fix --noPrompt --config .solhint.test.json --ignore-path .solhintignore"], "**/*.{json,md,yml,yaml}": ["prettier --write"]}, "keywords": [], "author": "", "license": "BUSL-1.1", "devDependencies": {"@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "@types/node": "^20.11.14", "husky": "^9.0.7", "lint-staged": "^15.2.1", "prettier": "^3.2.4", "solhint": "^5.1.0"}}