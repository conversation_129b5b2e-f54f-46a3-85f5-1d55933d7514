// SPDX-License-Identifier: BUSL-1.1

pragma solidity 0.8.28;

import { Clones } from "@openzeppelin/contracts/proxy/Clones.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { IERC20Metadata } from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import { IERC20Permit } from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol";

import { console } from "forge-std/console.sol";
import { BaseTest } from "test/utils/BaseTest.t.sol";

import { AssetRegistry } from "src/AssetRegistry.sol";
import { BasketToken } from "src/BasketToken.sol";

interface IVyperPermit {
    function permit(
        address owner,
        address spender,
        uint256 amount,
        uint256 expiry,
        bytes calldata signature
    )
        external
        returns (bool);
}

contract Permit2Test is BaseTest {
    BasketToken public basket;
    BasketToken public basket2;

    function setUp() public override {
        forkNetworkAt("mainnet", BLOCK_NUMBER_MAINNET_FORK);
        super.setUp();
        address assetRegistry = createUser("assetRegistry");
        address implementation = address(new BasketToken());
        basket = BasketToken(Clones.clone(implementation));
        basket2 = BasketToken(Clones.clone(implementation));
        basket.initialize((IERC20(ETH_WEETH)), "test", "TEST", 1, address(1), assetRegistry);
        basket2.initialize((IERC20(ETH_WETH)), "test2", "TEST2", 8, address(1), assetRegistry);

        // mock call to return ENABLED for the asset
        vm.mockCall(
            address(assetRegistry), abi.encodeCall(AssetRegistry.hasPausedAssets, basket.bitFlag()), abi.encode(false)
        );
        vm.mockCall(
            address(assetRegistry), abi.encodeCall(AssetRegistry.hasPausedAssets, basket2.bitFlag()), abi.encode(false)
        );
    }

    // Testing for ERC-2612 compatible tokens, without using Permit2
    function testFuzz_multicallPermit_requestDeposit_erc2612(uint256 amount) public {
        amount = bound(amount, 1, type(uint160).max);
        (address from, uint256 key) = makeAddrAndKey("bob");

        address asset = BasketToken(basket).asset();
        deal(asset, from, amount);

        // No direct approval exists
        assertEq(IERC20(asset).allowance(from, address(basket)), 0);

        uint256 deadline = vm.getBlockTimestamp() + 1000;

        // Generate the ERC-2612 signature
        (uint8 v, bytes32 r, bytes32 s) = _generatePermitSignature(asset, from, key, address(basket), amount, deadline);

        // Use multicall to call permit2 and requestDeposit
        bytes[] memory data = new bytes[](2);
        data[0] = abi.encodeWithSelector(
            BasketToken.permit2.selector, IERC20(address(asset)), from, address(basket), amount, deadline, v, r, s
        );
        data[1] = abi.encodeWithSelector(BasketToken.requestDeposit.selector, amount, from, from);
        vm.prank(from);
        basket.multicall(data);

        // Check state and verify it worked without doing any approval tx.
        assertEq(basket.pendingDepositRequest(2, from), amount);
    }

    // Testing for non-permit tokens, using Permit2
    function testFuzz_multicallPermit_requestDeposit_permit2(uint256 amount) public {
        amount = bound(amount, 1, type(uint160).max);
        (address from, uint256 key) = makeAddrAndKey("bob");

        address asset = BasketToken(basket2).asset();
        deal(asset, from, amount);

        // Allow Permit2 to spend the asset
        vm.prank(from);
        IERC20(asset).approve(ETH_PERMIT2, _MAX_UINT256);

        uint256 deadline = vm.getBlockTimestamp() + 1000;

        // Generate the Permit2 signature
        (uint8 v, bytes32 r, bytes32 s) =
            _generatePermit2Signature(asset, from, key, address(basket2), amount, deadline);

        // Use multicall to call permit2 and requestDeposit
        bytes[] memory data = new bytes[](2);
        data[0] = abi.encodeWithSelector(
            BasketToken.permit2.selector, IERC20(address(asset)), from, address(basket2), amount, deadline, v, r, s
        );
        data[1] = abi.encodeWithSelector(BasketToken.requestDeposit.selector, amount, from, from);
        vm.prank(from);
        basket2.multicall(data);

        // Check state
        assertEq(basket2.pendingDepositRequest(2, from), amount);
    }

    function test_confirm_permit_standard_availability() public {
        address[44] memory assets = [
            // Yearn Vaults and Gauges
            ******************************************, // MAINNET_ETH_YFI_VAULT_V2
            ******************************************, // MAINNET_ETH_YFI_GAUGE
            ******************************************, // MAINNET_DYFI_ETH_VAULT_V2
            ******************************************, // MAINNET_DYFI_ETH_GAUGE
            ******************************************, // MAINNET_WETH_YETH_VAULT_V2
            ******************************************, // MAINNET_WETH_YETH_GAUGE
            ******************************************, // MAINNET_PRISMA_YPRISMA_VAULT_V2
            ******************************************, // MAINNET_PRISMA_YPRISMA_GAUGE
            ******************************************, // MAINNET_CRV_YCRV_VAULT_V2
            ******************************************, // MAINNET_CRV_YCRV_GAUGE
            ******************************************, // MAINNET_YVUSDC_VAULT_V3
            ******************************************, // MAINNET_YVUSDC_GAUGE
            ******************************************, // MAINNET_YVDAI_VAULT_V3
            ******************************************, // MAINNET_YVDAI_GAUGE
            ******************************************, // MAINNET_YVWETH_VAULT_V3
            ******************************************, // MAINNET_YVWETH_GAUGE
            ******************************************, // MAINNET_COVEYFI_YFI_VAULT_V2
            ******************************************, // MAINNET_COVEYFI_YFI_GAUGE
            ******************************************, // MAINNET_YVDAI_VAULT_V3_2
            ******************************************, // MAINNET_YVDAI_2_GAUGE
            ******************************************, // MAINNET_YVWETH_VAULT_V3_2
            ******************************************, // MAINNET_YVWETH_2_GAUGE
            ******************************************, // MAINNET_YVCRVUSD_VAULT_V3_2
            ******************************************, // MAINNET_YVCRVUSD_2_GAUGE
            // Curve LP Tokens and Pools
            ******************************************, // MAINNET_CRV3POOL_LP_TOKEN
            ******************************************, // MAINNET_DYFI_ETH_POOL_LP_TOKEN
            ******************************************, // MAINNET_TRI_CRYPTO_USDC
            ******************************************, // MAINNET_TRI_CRYPTO_2_LP_TOKEN
            ******************************************, // MAINNET_ETH_YFI_POOL_LP_TOKEN
            ******************************************, // MAINNET_FRAX_USDC_POOL_LP_TOKEN
            ******************************************, // MAINNET_WETH_YETH_POOL
            ******************************************, // MAINNET_PRISMA_YPRISMA_POOL
            ******************************************, // MAINNET_CRV_YCRV_POOL
            ******************************************, // MAINNET_COVEYFI_YFI_POOL
            // Other tokens
            ******************************************, // MAINNET_DYFI
            ******************************************, // MAINNET_USDC
            ******************************************, // MAINNET_USDT
            ******************************************, // MAINNET_WETH
            ******************************************, // MAINNET_YFI
            ******************************************, // MAINNET_FRAX
            ******************************************, // MAINNET_CRV
            ******************************************, // MAINNET_YCRV
            ******************************************, // MAINNET_PRISMA
            ****************************************** // MAINNET_CRVUSD
        ];

        (address from, uint256 key) = makeAddrAndKey("bob");

        // Iterate through each asset to test ERC-2612 permit compatibility
        for (uint256 i = 0; i < assets.length; i++) {
            address asset = assets[i];
            string memory symbol = IERC20Metadata(asset).symbol();

            // First check if token implements DOMAIN_SEPARATOR() from ERC-2612
            try IERC20Permit(asset).DOMAIN_SEPARATOR() returns (bytes32) {
                // Generate permit signature for max approval
                (uint8 v, bytes32 r, bytes32 s) =
                    _generatePermitSignature(asset, from, key, address(this), UINT256_MAX, UINT256_MAX);

                // Store initial approval amount
                uint256 approvalBefore = IERC20(asset).allowance(from, address(this));

                // Try standard ERC-2612 permit first
                try IERC20Permit(asset).permit(from, address(this), UINT256_MAX, UINT256_MAX, v, r, s) {
                    uint256 approvalAfter = IERC20(asset).allowance(from, address(this));
                    assertGt(approvalAfter, approvalBefore);
                    console.log(string.concat(unicode"✅ ", vm.toString(asset), " ", symbol, " implements ERC-2612"));
                } catch {
                    // If standard permit fails, try Vyper-style permit which takes signature as packed bytes r, s, v
                    try IVyperPermit(asset).permit(
                        from, address(this), UINT256_MAX, UINT256_MAX, abi.encodePacked(r, s, v)
                    ) {
                        uint256 approvalAfter = IERC20(asset).allowance(from, address(this));
                        assertGt(approvalAfter, approvalBefore);
                        console.log(
                            string.concat(
                                unicode"😵‍ ",
                                vm.toString(asset),
                                " ",
                                symbol,
                                " implements ERC-2612 (with last param as bytes r, s, v)"
                            )
                        );
                    } catch {
                        // Both permit attempts failed
                        console.log(string.concat(unicode"❓ ", vm.toString(asset), " ", symbol, " failed to permit"));
                    }
                }
            } catch {
                // Token does not implement DOMAIN_SEPARATOR, so not ERC-2612 compatible
                console.log(string.concat(unicode"❌ ", vm.toString(asset), " ", symbol, " does not implement ERC-2612"));
            }
        }
    }
}
