// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.28;

import { CommonBase } from "forge-std/Base.sol";

contract Constants is CommonBase {
    bytes32 public constant DEFAULT_ADMIN_ROLE = 0x00;
    bytes32 public constant BASKET_MANAGER_ROLE = keccak256("BASKET_MANAGER_ROLE");
    bytes32 public constant MANAGER_ROLE = keccak256("MANAGER_ROLE");
    bytes32 public constant REBALANCE_PROPOSER_ROLE = keccak256("REBALANCE_PROPOSER_ROLE");
    bytes32 public constant TOKENSWAP_PROPOSER_ROLE = keccak256("TOKENSWAP_PROPOSER_ROLE");
    bytes32 public constant TOKENSWAP_EXECUTOR_ROLE = keccak256("TOKENSWAP_EXECUTOR_ROLE");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    bytes32 public constant BASKET_TOKEN_ROLE = keccak256("BASKET_TOKEN_ROLE");
    bytes32 public constant TIMELOCK_ROLE = keccak256("TIMELOCK_ROLE");
    bytes32 public constant _WEIGHT_STRATEGY_ROLE = keccak256("WEIGHT_STRATEGY_ROLE");

    // Interface IDs
    bytes4 public constant OPERATOR7540_INTERFACE = 0xe3bc4e65;
    bytes4 public constant ASYNCHRONOUS_DEPOSIT_INTERFACE = 0xce3bbe50;
    bytes4 public constant ASYNCHRONOUS_REDEMPTION_INTERFACE = 0x620ee8e4;

    // PERMIT (ERC-2612)
    // keccak256("Permit(address owner,address spender,uint256 value,uint256 nonce,uint256 deadline)");
    bytes32 public constant PERMIT_TYPEHASH = 0x6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9;

    // PERMIT 2
    /// @dev The address of the Permit2 contract the library will use.
    address internal constant ETH_PERMIT2 = ******************************************;

    // ERC1271 Magic Value
    bytes4 public constant ERC1271_MAGIC_VALUE = 0x1626ba7e;

    address public constant CREATE3_FACTORY = ******************************************;
    // Ref: https://github.com/euler-xyz/euler-price-oracle/blob/experiments/test/adapter/pyth/PythFeeds.sol
    address public constant PYTH = ******************************************;
    address public constant ETH = ******************************************;
    address public constant USD = address(840); // USD ISO 4217 currency code
    address public constant WBTC = ******************************************;

    // EXTERNAL WEIGHT STRATEGISTS
    address public constant GAUNTLET_STRATEGIST = ******************************************;

    // ASSET ADDRESSES
    address public constant ETH_CBBTC = ******************************************;
    address public constant ETH_EZETH = ******************************************;
    address public constant ETH_GHO = ******************************************;
    address public constant ETH_RETH = ******************************************;
    address public constant ETH_RSETH = ******************************************;
    address public constant ETH_SUSDE = ******************************************;
    address public constant ETH_TBTC = ******************************************;
    address public constant ETH_USDT = ******************************************;
    address public constant ETH_WBTC = ******************************************;
    address public constant ETH_WEETH = ******************************************;
    address public constant ETH_WETH = ******************************************;
    address public constant ETH_SDAI = ******************************************;
    address public constant ETH_SFRAX = ******************************************;
    address public constant ETH_USDC = ******************************************;
    address public constant ETH_DAI = ******************************************;
    address public constant ETH_FRAX = ******************************************;
    address public constant ETH_USDE = ******************************************;
    address public constant ETH_SFRXUSD = ******************************************;
    address public constant ETH_FRXUSD = ******************************************;
    address public constant ETH_YSYG_YVUSDS_1 = ******************************************;
    address public constant ETH_USDS = ******************************************;
    address public constant ETH_SUPERUSDC = ******************************************;
    address public constant ETH_COVE = ******************************************;

    // PRICE FEEDS
    // USDC/USD
    bytes32 public constant PYTH_USDC_USD_FEED = 0xeaa020c61cc479712813461ce153894a96a6c00b21ed0cfc2798d1f9a9e9c94a;
    address public constant ETH_CHAINLINK_USDC_USD_FEED = ******************************************;

    // DAI/USD
    bytes32 public constant PYTH_DAI_USD_FEED = 0xb0948a5e5313200c632b51bb5ca32f6de0d36e9950a942d19751e833f70dabfd;
    address public constant ETH_CHAINLINK_DAI_USD_FEED = ******************************************;

    // sDAI/USD
    bytes32 public constant PYTH_SDAI_USD_FEED = 0x710659c5a68e2416ce4264ca8d50d34acc20041d91289110eea152c52ff3dc39;
    // Missing Chainlink price feed

    // FRAX/USD
    bytes32 public constant PYTH_FRAX_USD_FEED = 0xc3d5d8d6d17081b3d0bbca6e2fa3a6704bb9a9561d9f9e1dc52db47629f862ad;
    address public constant ETH_CHAINLINK_FRAX_USD_FEED = ******************************************;

    // USDE/USD
    bytes32 public constant PYTH_USDE_USD_FEED = 0x6ec879b1e9963de5ee97e9c8710b742d6228252a5e2ca12d4ae81d7fe5ee8c5d;
    address public constant ETH_CHAINLINK_USDE_USD_FEED = ******************************************;

    // ETH/USD
    bytes32 public constant PYTH_ETH_USD_FEED = 0xff61491a931112ddf1bd8147cd1b641375f79f5825126d665480874634fd0ace;
    address public constant ETH_CHAINLINK_ETH_USD_FEED = ******************************************;

    // SUSDE/USD
    bytes32 public constant PYTH_SUSDE_USD_FEED = 0xca3ba9a619a4b3755c10ac7d5e760275aa95e9823d38a84fedd416856cdba37c;
    address public constant ETH_CHAINLINK_SUSDE_USD_FEED = ******************************************;

    // weETH/ETH
    bytes32 public constant PYTH_WEETH_USD_FEED = 0x9ee4e7c60b940440a261eb54b6d8149c23b580ed7da3139f7f08f4ea29dad395;
    address public constant ETH_CHAINLINK_WEETH_ETH_FEED = ******************************************;

    // ezETH/ETH
    // TODO: pyth price feed not found
    bytes32 public constant PYTH_EZETH_USD_FEED = 0x06c217a791f5c4f988b36629af4cb88fad827b2485400a358f3b02886b54de92;
    address public constant ETH_CHAINLINK_EZETH_ETH_FEED = ******************************************;

    // rsETH/ETH
    // TODO: pyth price feed not found
    bytes32 public constant PYTH_RSETH_USD_FEED = 0x0caec284d34d836ca325cf7b3256c078c597bc052fbd3c0283d52b581d68d71f;
    address public constant ETH_CHAINLINK_RSETH_ETH_FEED = ******************************************;

    // rETH/ETH
    bytes32 public constant PYTH_RETH_USD_FEED = 0xa0255134973f4fdf2f8f7808354274a3b1ebc6ee438be898d045e8b56ba1fe13;
    address public constant ETH_CHAINLINK_RETH_ETH_FEED = ******************************************;

    // wBTC/BTC
    bytes32 public constant PYTH_WBTC_USD_FEED = 0xc9d8b075a5c69303365ae23633d4e085199bf5c520a3b90fed1322a0342ffc33;
    address public constant ETH_CHAINLINK_WBTC_BTC_FEED = ******************************************;

    // tBTC/BTC
    bytes32 public constant PYTH_TBTC_USD_FEED = 0x56a3121958b01f99fdc4e1fd01e81050602c7ace3a571918bb55c6a96657cca9;
    address public constant ETH_CHAINLINK_TBTC_BTC_FEED = ******************************************;

    // GHO/USD
    // TODO: pyth price feed not found
    bytes32 public constant PYTH_GHO_USD_FEED = 0x2a0e948f637a8c251d9f06055e72eb4b3880dd57848bbdb02993c8165d7df4ee;
    address public constant ETH_CHAINLINK_GHO_USD_FEED = ******************************************;

    // cbBTC/USD
    // TODO: pyth price feed not found
    bytes32 public constant PYTH_CBBTC_USD_FEED = 0x2817d7bfe5c64b8ea956e9a26f573ef64e72e4d7891f2d6af9bcc93f7aff9a97;
    address public constant ETH_CHAINLINK_CBBTC_USD_FEED = ******************************************;

    // sfrxUSD/sUSDe
    address public constant ETH_CURVE_SFRXUSD_SUSDE_POOL = ******************************************;

    // frxUSD
    bytes32 public constant PYTH_FRXUSD_USD_FEED = 0x7c53208632935ba5122c3cf65a0f4b3e72ba4955b49ad6ba0acf3d9ba405aef3;
    address public constant ETH_CHAINLINK_FRXUSD_USD_FEED = ******************************************;

    // USDS
    bytes32 public constant PYTH_USDS_USD_FEED = 0x77f0971af11cc8bac224917275c1bf55f2319ed5c654a1ca955c82fa2d297ea1;
    address public constant ETH_CHAINLINK_USDS_USD_FEED = ******************************************;

    // COVE
    address public constant COVE_DEPLOYER_ADDRESS = ******************************************;
    address public constant COVE_OPS_MULTISIG = ******************************************;
    address public constant COVE_COMMUNITY_MULTISIG = ******************************************;
    address public constant COVE_MASTER_REGISTRY = ******************************************;

    // AWS KEEPER ACCOUNTS
    address public constant BOOSTIES_SILVERBACK_AWS_ACCOUNT = ******************************************;
    address public constant STAGING_COVE_SILVERBACK_AWS_ACCOUNT = ******************************************;
    address public constant PRODUCTION_COVE_SILVERBACK_AWS_ACCOUNT = ******************************************;

    // SPONSORS
    address public constant SPONSOR_GAUNTLET = ******************************************;

    // STAGING
    // 1 out of 5 addresses, ops multisig signers + deployer
    // https://app.safe.global/settings/setup?safe=eth:******************************************
    address public constant COVE_STAGING_OPS_MULTISIG = ******************************************;
    // 1 out of 5 addresses, ops multisig signers + deployer
    // https://app.safe.global/settings/setup?safe=eth:******************************************
    address public constant COVE_STAGING_COMMUNITY_MULTISIG = ******************************************;
    address public constant COVE_STAGING_MASTER_REGISTRY = ******************************************;

    // Constants hardcoded in the contracts, replicated here for testing.
    uint16 public constant MAX_MANAGEMENT_FEE = 3000;
    uint16 public constant MANAGEMENT_FEE_DECIMALS = 1e4;
    uint16 public constant MAX_SWAP_FEE = 500;
    uint8 public constant MAX_RETRIES = 10;
    uint256 public constant REBALANCE_COOLDOWN_SEC = 1 hours;
    uint256 public constant MAX_SLIPPAGE_LIMIT = 0.5e18;
    uint256 public constant MAX_WEIGHT_DEVIATION_LIMIT = 0.5e18;
    uint256 public constant MIN_STEP_DELAY = 1 minutes;
    uint256 public constant MAX_STEP_DELAY = 60 minutes;

    // https://evc.wtf/docs/contracts/deployment-addresses/
    address public constant EVC = ******************************************;

    // This block number includes all pyth oracle updates
    // https://etherscan.io/tx/0xdb4a012e6c07cc6417c6c2fd020e110ea40fd1207221bbbc5e346045b9b26ecd
    uint256 public constant BLOCK_NUMBER_MAINNET_FORK = 21_792_603;

    function labelKnownAddresses() public {
        // Cove addresses
        vm.label(COVE_DEPLOYER_ADDRESS, "COVE DEPLOYER");
        vm.label(COVE_OPS_MULTISIG, "COVE OPS MULTISIG");
        vm.label(COVE_COMMUNITY_MULTISIG, "COVE COMMUNITY MULTISIG");
        vm.label(COVE_MASTER_REGISTRY, "COVE MASTER REGISTRY");
        vm.label(BOOSTIES_SILVERBACK_AWS_ACCOUNT, "BOOSTIES SILVERBACK AWS ACCOUNT");
        vm.label(STAGING_COVE_SILVERBACK_AWS_ACCOUNT, "STAGING COVE SILVERBACK AWS ACCOUNT");
        vm.label(COVE_STAGING_OPS_MULTISIG, "COVE STAGING OPS MULTISIG");
        vm.label(COVE_STAGING_COMMUNITY_MULTISIG, "COVE STAGING COMMUNITY MULTISIG");

        // Infrastructure addresses
        vm.label(CREATE3_FACTORY, "CREATE3 FACTORY");
        vm.label(PYTH, "PYTH ORACLE");
        vm.label(ETH_PERMIT2, "PERMIT2");
        vm.label(EVC, "EVC");

        // Base tokens
        vm.label(ETH, "ETH");
        vm.label(USD, "USD");
        vm.label(WBTC, "WBTC");

        // Asset addresses
        vm.label(ETH_CBBTC, "cbBTC");
        vm.label(ETH_EZETH, "ezETH");
        vm.label(ETH_GHO, "GHO");
        vm.label(ETH_RETH, "rETH");
        vm.label(ETH_RSETH, "rsETH");
        vm.label(ETH_SUSDE, "sUSDe");
        vm.label(ETH_TBTC, "tBTC");
        vm.label(ETH_USDT, "USDT");
        vm.label(ETH_WBTC, "WBTC");
        vm.label(ETH_WEETH, "weETH");
        vm.label(ETH_WETH, "WETH");
        vm.label(ETH_SDAI, "sDAI");
        vm.label(ETH_SFRAX, "sFRAX");
        vm.label(ETH_USDC, "USDC");
        vm.label(ETH_DAI, "DAI");
        vm.label(ETH_FRAX, "FRAX");
        vm.label(ETH_USDE, "USDe");
        vm.label(ETH_SUPERUSDC, "SuperUSDC");
        vm.label(ETH_YSYG_YVUSDS_1, "ysyg_yvUSDS-1");
        vm.label(ETH_USDS, "USDS");
        vm.label(ETH_SFRXUSD, "sfrxUSD");
        vm.label(ETH_FRXUSD, "frxUSD");

        // Price feeds
        vm.label(ETH_CHAINLINK_USDC_USD_FEED, "CHAINLINK USDC/USD");
        vm.label(ETH_CHAINLINK_DAI_USD_FEED, "CHAINLINK DAI/USD");
        vm.label(ETH_CHAINLINK_FRAX_USD_FEED, "CHAINLINK FRAX/USD");
        vm.label(ETH_CHAINLINK_USDE_USD_FEED, "CHAINLINK USDe/USD");
        vm.label(ETH_CHAINLINK_ETH_USD_FEED, "CHAINLINK ETH/USD");
        vm.label(ETH_CHAINLINK_SUSDE_USD_FEED, "CHAINLINK sUSDe/USD");
        vm.label(ETH_CHAINLINK_WEETH_ETH_FEED, "CHAINLINK weETH/ETH");
        vm.label(ETH_CHAINLINK_EZETH_ETH_FEED, "CHAINLINK ezETH/ETH");
        vm.label(ETH_CHAINLINK_RSETH_ETH_FEED, "CHAINLINK rsETH/ETH");
        vm.label(ETH_CHAINLINK_RETH_ETH_FEED, "CHAINLINK rETH/ETH");
        vm.label(ETH_CHAINLINK_WBTC_BTC_FEED, "CHAINLINK WBTC/BTC");
        vm.label(ETH_CHAINLINK_TBTC_BTC_FEED, "CHAINLINK tBTC/BTC");
        vm.label(ETH_CHAINLINK_GHO_USD_FEED, "CHAINLINK GHO/USD");
        vm.label(ETH_CHAINLINK_CBBTC_USD_FEED, "CHAINLINK cbBTC/USD");

        // External addresses
        vm.label(GAUNTLET_STRATEGIST, "GAUNTLET STRATEGIST");
    }
}
